#!/usr/bin/env python3
"""
Test script for the add_education_embedding functionality.
This script tests the new education embedding feature in OpenSearch.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from database import sessionLocal
from services.external.opensearch import OpenSearchServiceClass
from dependencies import OPENSEARCH_RC_INDEX
from models.user import User
from models.user_education import UserEducation
from models.school import School
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_add_education_embedding():
    """Test the add_education_embedding function"""
    db = sessionLocal()
    
    try:
        # Find a user with education data
        user_with_education = (
            db.query(User.id)
            .join(UserEducation, User.id == UserEducation.user_id)
            .first()
        )
        
        if not user_with_education:
            logger.error("No user found with education data")
            return False
        
        user_id = user_with_education[0]
        logger.info(f"Testing with user_id: {user_id}")
        
        # Check if user exists in OpenSearch
        opensearch_service = OpenSearchServiceClass(OPENSEARCH_RC_INDEX)
        exists = opensearch_service.check_id_exists(user_id)
        
        if not exists:
            logger.error(f"User {user_id} does not exist in OpenSearch")
            return False
        
        logger.info(f"User {user_id} exists in OpenSearch")
        
        # Test the add_education_embedding function
        logger.info("Testing add_education_embedding function...")
        result = opensearch_service.add_education_embedding(user_id, db)
        
        if result:
            logger.info("✅ Education embedding added successfully!")
            
            # Verify the embedding was added by checking the document
            doc = opensearch_service.get_single_document(user_id)
            if doc and "embeddings" in doc and "education" in doc["embeddings"]:
                logger.info("✅ Education embedding verified in OpenSearch document")
                logger.info(f"Education embedding dimension: {len(doc['embeddings']['education'])}")
                return True
            else:
                logger.error("❌ Education embedding not found in document")
                return False
        else:
            logger.error("❌ Failed to add education embedding")
            return False
            
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        return False
    finally:
        db.close()

def test_update_address_with_education_embedding():
    """Test the update_address_in_opensearch function with education embedding"""
    db = sessionLocal()
    
    try:
        # Find a user with education data
        user_with_education = (
            db.query(User.id)
            .join(UserEducation, User.id == UserEducation.user_id)
            .first()
        )
        
        if not user_with_education:
            logger.error("No user found with education data")
            return False
        
        user_id = user_with_education[0]
        logger.info(f"Testing update_address_in_opensearch with user_id: {user_id}")
        
        # Check if user exists in OpenSearch
        opensearch_service = OpenSearchServiceClass(OPENSEARCH_RC_INDEX)
        exists = opensearch_service.check_id_exists(user_id)
        
        if not exists:
            logger.error(f"User {user_id} does not exist in OpenSearch")
            return False
        
        # Test with empty update payload (should trigger education embedding)
        logger.info("Testing update_address_in_opensearch with empty payload...")
        result = opensearch_service.update_address_in_opensearch(user_id, {}, db)
        
        logger.info("✅ update_address_in_opensearch completed successfully!")
        
        # Verify the embedding was added
        doc = opensearch_service.get_single_document(user_id)
        if doc and "embeddings" in doc and "education" in doc["embeddings"]:
            logger.info("✅ Education embedding verified after update_address_in_opensearch")
            return True
        else:
            logger.info("ℹ️ Education embedding not found (may be expected if no education data)")
            return True
            
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        return False
    finally:
        db.close()

def main():
    """Run all tests"""
    logger.info("🚀 Starting education embedding tests...")
    
    # Test 1: Direct function test
    logger.info("\n" + "="*50)
    logger.info("TEST 1: Direct add_education_embedding function")
    logger.info("="*50)
    test1_result = test_add_education_embedding()
    
    # Test 2: Integration test with update_address_in_opensearch
    logger.info("\n" + "="*50)
    logger.info("TEST 2: Integration with update_address_in_opensearch")
    logger.info("="*50)
    test2_result = test_update_address_with_education_embedding()
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    logger.info(f"Test 1 (Direct function): {'✅ PASSED' if test1_result else '❌ FAILED'}")
    logger.info(f"Test 2 (Integration): {'✅ PASSED' if test2_result else '❌ FAILED'}")
    
    if test1_result and test2_result:
        logger.info("🎉 All tests passed!")
        return True
    else:
        logger.error("❌ Some tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
