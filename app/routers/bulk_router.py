from celery_tasks.enrich_company_info import enrich_company_info_bulk_all_users_service, enrich_company_info_bulk_linkedinID_service
from .common_imports import *
from fastapi import APIRouter, Depends, status, HTTPException, Request
from database import get_db
from sqlalchemy.orm import Session
from services.scrapper.scrapping_dog import ScrappingDogServiceClass
from models.user import User
from models.interview_feedback import InterviewFeedback
from sqlalchemy import and_

router = APIRouter()

@router.get("/enrich-user-company-info-bulk")
def scrape_bulk_interview_feedback(request: Request):
    try:
        enrich_company_info_bulk_all_users_service.delay()

        return ApiSuccessResponse(message="Success", data=f"Started process in celery")

    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/fix-user-company-details-bulk")
def scrape_bulk(request: Request):
    try:
        enrich_company_info_bulk_linkedinID_service.delay()

        return ApiSuccessResponse(message="Success", data=f"Started process in celery")

    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
