from fastapi import APIRouter, status, HTTPException
from .common_imports import *
from services.recruiter.outreach import RecruiterOutreachServiceClass
from celery_tasks.onboarding_tasks import (
    send_recruiter_outreach_email,
    send_cold_email_to_recruiter,
)
from celery_tasks.clay_requisition_tasks import create_clay_requisition

router = APIRouter(prefix="/out-reach", tags=["OutReach"])

recruiter_outreache_service_object = RecruiterOutreachServiceClass()


@router.post(
    '/send-recruiter-email/{outreach_id}',
)
def send_outreach_email(
    outreach_id: int
):
    try:        
        task = send_recruiter_outreach_email.apply_async(args=[outreach_id])
        return ApiSuccessResponse(
            data={"task_id": task.id},
            message="Email sent successfully",
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post('/generate-recruiter-outreach-email/{requisition_id}')
def generate_recruiter_outreach_email(
    requisition_id: int,
    db: Session = Depends(get_db)
):
    try:
        task = send_cold_email_to_recruiter.delay(requisition_id)
        return ApiSuccessResponse(
            data={"task_id": task.id},
            message="Email sent successfully",
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post('/create-recruiter-outreach-for-clay')
def create_recruiter_outreach_for_clay(
    payload: dict,
    db: Session = Depends(get_db)
):
    try:
        create_clay_requisition.delay(payload)
        return ApiSuccessResponse(
            data={},
            message="Recruiter outreach created successfully"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.post('/resend-initiated-email')
def resend_initiated_email(
    payload: dict,
    db: Session = Depends(get_db)
):
    if payload['hash'] != "$2b$12$tskKHuC5h5FtKk1Cu7MwjukPKmlC1DKnp3Le5QTFblFjohEAuCYvq":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid hash"
        )
    try:
        from models.recruiter_outreach import RecruiterOutreach

        outreach_data = (
            recruiter_outreache_service_object.get_recruiter_outreach_by_status(
                RecruiterOutreach.EMAIL_INITIATED, db
            )
        )

        for outreach in outreach_data:
            send_recruiter_outreach_email.delay(outreach.id)

        return ApiSuccessResponse(
            data={},
            message="Email dispatch successfully"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
