from schemas.update_requisition import UpdateRequisitionSchema
from celery_tasks.post_requisition_process_tasks import create_requisition_vector_embeddings
from services.requisition.requisition import RequisitionServiceClass
from celery_tasks.post_requisition_process_tasks import create_evaluation_criteria
from .common_imports import *
import logging
import json
from schemas.requisition import RequisitionID,CandidateEvaluationPayload
from fastapi import APIRouter, Depends, HTTPException, status
from celery_tasks.post_requisition_process_tasks import get_requisition_requirements
from celery_tasks.candidate_tasks import evaluate_candidate

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/admin", tags=["admin"]
)


@router.post(
    "/update-requisition"
)
def update_requisition(
    request: UpdateRequisitionSchema,
    db: Session = Depends(get_db),
):
    try:
        # get requisition
        requisition = RequisitionServiceClass().get_requisition_by_id(
            request.requisition_id, db)

        if not requisition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Requisition not found",
            )

        # update requisition details
        if request.title or request.llm_requistion_details or request.binary_requirements:
            requisition.title = request.title if request.title else requisition.title
            requisition.llm_requistion_details = json.dumps(
                request.llm_requistion_details) if request.llm_requistion_details else requisition.llm_requistion_details
            requisition.binary_requirements = json.dumps(
                request.binary_requirements) if request.binary_requirements else requisition.binary_requirements

            db.commit()
            db.refresh(requisition)

        # create evaluation criteria
        create_evaluation_criteria.delay(request.requisition_id)

        # create requisition vector embeddings
        create_requisition_vector_embeddings.delay(request.requisition_id)

        logger.info(
            f"Successfully dispatched requisition update task for requisition: {request.requisition_id}"
        )

        return ApiSuccessResponse(message="Success", data=True)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
    
@router.post("/get-requisition-requirements")
def retrieve_requisition_requirements(
    inputData:RequisitionID,
):
    try:
        response = get_requisition_requirements.delay(inputData.requisition_id)
        return ApiSuccessResponse(
            message="Requisition requirements fetched successfully", data=True)
    
    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    

@router.post("/evaluate-candidate")
def evaluate_candidate_task(
    inputData: CandidateEvaluationPayload,
):
    try:
        response = evaluate_candidate.delay(
            inputData.user_id, inputData.requisition_id)
        return ApiSuccessResponse(
            message="Candidate evaluation task dispatched successfully", data=True)
    
    except EntityNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
