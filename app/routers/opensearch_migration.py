from fastapi import APIRouter, Depends, status, HTTPException
from database import get_db
from sqlalchemy.orm import Session
from .common_imports import *
from services.external.openai import OpenAIServiceClass
from services.interview.interview import InterviewServiceClass
from services.requisition.requisition import RequisitionServiceClass
from services.parser.requisition_parser import RequisitionParserServiceClass
import logging
from services.external.opensearch import OpenSearchServiceClass
from dependencies import OPENSEARCH_RC_INDEX
from celery_tasks.migration_opensearch import migrate_data, migrate_data_users_table, migrate_data_update, insert_users_to_opensearch, education_update
from services.interview.interview import InterviewServiceClass
logger = logging.getLogger(__name__)


router = APIRouter()
requsition_service = RequisitionServiceClass()
requisition_parser_service = RequisitionParserServiceClass()
open_ai_service_object = OpenAIServiceClass()
interview_service = InterviewServiceClass()

router = APIRouter(prefix="/migration", tags=["Migration"])


@router.get(
    "/insert"
)
def get_requisition(user_id: str, db: Session = Depends(get_db)):
    try:
        # Check if the person ID already exists before converting data
        opensearch_service = OpenSearchServiceClass(
            index_name=OPENSEARCH_RC_INDEX)
        exists = opensearch_service.check_id_exists(user_id)
        logger.info(f"Exists: {exists}")

        if exists:
            return ApiSuccessResponse(
                message="Person ID already exists. No conversion performed."
            )

        # Proceed with data conversion and insertion
        response = interview_service.convert_data(user_id, db)

        if response['feedback'] == []:
            return ApiSuccessResponse(
                message="Feedback doesn't exist for given user, not inserting", data={"inserted_data": None, "opensearch_response": None}
            )

        logger.info("Data converted.. now inserting..")

        resp = interview_service.insert_record_to_opensearch(response, db)

        return ApiSuccessResponse(
            message="Data converted and inserted successfully", data={"inserted_data": response, "opensearch_response": resp}
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/bulk-convert-insert"
)
def bulk_insert(db: Session = Depends(get_db)):
    try:
        migrate_data.delay()
        return ApiSuccessResponse(
            message="Data inserted successfully in bulk", data="null"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/bulk-convert-update"
)
def bulk_insert(db: Session = Depends(get_db)):
    try:
        migrate_data_update.delay()
        return ApiSuccessResponse(
            message="Data updated successfully in bulk", data="null"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get(
    "/bulk-convert-insert-users-table"
)
def bulk_insert(db: Session = Depends(get_db)):
    try:
        migrate_data_users_table.delay()
        return ApiSuccessResponse(
            message="Data inserted successfully in bulk", data="null"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/bulk-insert-users")
def bulk_insert_users():
    try:
        task = insert_users_to_opensearch.delay()

        return ApiSuccessResponse(message="Success", data=task.id)
    except Exception as e:
        logger.error(f"Error in get_user_ids: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get("/education-update")
def education_embedding_update():
    """
    Trigger bulk education embedding update for all OpenSearch records.
    This will process all records in OpenSearch, extract education data (description and school name),
    generate embeddings using OpenAI, and store them in embeddings.education field.
    """
    try:
        task = education_update.delay()

        return ApiSuccessResponse(
            message="Education embedding update started successfully",
            data={"task_id": task.id}
        )
    except Exception as e:
        logger.error(f"Error starting education embedding update: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
