from .common_imports import *
from services.automate_user_profile import InterviewInviteServiceClass
from fastapi import APIRouter, Depends, status, HTTPException, Request
from database import get_db
from sqlalchemy.orm import Session
from typing import Dict
from services.media.video_processing import VideoProcessingServiceClass
from services.JD_matching import JDMatchingServiceClass

router = APIRouter()
interview_invite_object = InterviewInviteServiceClass()
jd_matching_object = JDMatchingServiceClass()


@router.post("/automate-user-profile")
def read_csv(
    request: Request,inputData: Dict[str, str], background_tasks: BackgroundTasks, db: Session = Depends(get_db)
):
    try:
        csv_link = inputData.get("csv_link")
        response = interview_invite_object.read_and_extract_variables(request,csv_link,background_tasks,db)
        return response
    except EntityNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.post("/JD-matching")
def JD_match_read_csv(
    request: Request,inputData: Dict[str, str], db: Session = Depends(get_db)
):
    try:
        csv_link = inputData.get("csv_link")
        response = jd_matching_object.read_and_extract_variables(request,csv_link,db)
        return response
    except EntityNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/populate-video-links/{requisition_id}")
def populate_video_links(
    requisition_id: int,
    background: BackgroundTasks,
    db: Session = Depends(get_db),
):
    try:
        from models.interview_feedback import InterviewFeedback
        from models.requisition import Requisition
        from dependencies import CANDIDATE_THRESHOLD_SCORE

        video_processing_service = VideoProcessingServiceClass()

        requisition = (
            db.query(Requisition)
            .filter(Requisition.id == requisition_id)
            .first()
        )
        interview_feedbacks = (
            db.query(InterviewFeedback)
            .filter(
                InterviewFeedback.requisition_id == requisition.id,
                InterviewFeedback.score >= CANDIDATE_THRESHOLD_SCORE,
                InterviewFeedback.status == 1,
            )
            .all()
        )
        for interview_feedback in interview_feedbacks:
            background.add_task(
                video_processing_service.map_video_link_to_question,
                interview_feedback,
                db,
            )
        return True
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/populate-requisition-embeddings")
def populate_requisition_embeddings(
    background: BackgroundTasks,
    db: Session = Depends(get_db),
):
    try:
        from models.requisition import Requisition
        from services.requisition.requisition_similarity import RequisitionSimilarity

        requisitions = db.query(Requisition).filter(Requisition.llm_requistion_details.isnot(None)).all()
        for requisition in requisitions:
            background.add_task(
                RequisitionSimilarity().store_embeddings, requisition.id, db
            )
        return True
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/remove-duplicate-cities")
def remove_duplicate_cities(
    inputData: Dict[str, list],
    db: Session = Depends(get_db),
):
    try:
        from services.city import CityServiceClass

        city_service = CityServiceClass()
        links = inputData.get("links", [])
        if not isinstance(links, list):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid input: 'links' should be an array.",
            )
        response = []
        for link in links:
            response.append(
                city_service.remove_duplicate_cities(
                    link,
                    db,
                )
            )
        return response
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
