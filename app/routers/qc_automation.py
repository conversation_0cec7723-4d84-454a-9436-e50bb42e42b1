from .common_imports import *
from fastapi import APIRouter, Depends, status, HTTPException, Request
from database import get_db
from sqlalchemy.orm import Session
from typing import Dict
from services.qc_automation.qc_automation import QCAutomationClass
from celery_tasks.qc_automation import qc_automation
qcautomation_service = QCAutomationClass()
from celery_tasks.qc_automation import qc_automation


router = APIRouter()


@router.get("/qc-automation")
def analyze_data(user_id):
    try:
        response = qc_automation.delay(user_id=user_id)
        return ApiSuccessResponse(message="Success", data="Task working on background")
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
