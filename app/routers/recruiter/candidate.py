from fastapi import APIRouter, Depends, status, HTTPException, Request
from schemas.candidate_interview import CandidateInterviewApiSuccessResponse
from schemas.shortlisted_candidate import ShortlistedCandidateAction
from ..common_imports import *
from services.recruiter.candidate_evaluation import CandidateEvaluationServiceClass
from schemas.interview_feedback import (
    InterviewF<PERSON>backListSchema,
    InterviewFeedbackReadSchema,
)
from typing import List
from schemas.shortlisted_candidate import (
    ShortlistedCandidateCreate,
    ShorlistedCandidateResponse,
)
from services.recruiter.candidate_activity import CandidateActivityServiceClass
from schemas.candidate_activity import CandidateActivityCreate, CandidateActivityBase
from dependencies import hash_id
from dependencies import RECRUITER_APP_HOST
from services.user.user import UserServiceClass

# , dependencies=[Depends(getCurrentUser)]
router = APIRouter(prefix="/recruiter/candidate", tags=["Candidate"])

candidate_evaluation_service_object = CandidateEvaluationServiceClass()
candidate_activity_service_object = CandidateActivityServiceClass()


@router.get(
    "/get-suitable-candidates/{requisition_id}",
    response_model=ApiSuccessResponse[List[InterviewFeedbackListSchema]],
)
def get_suitable_candidates(
    requisition_id: int, request: Request, db: Session = Depends(get_db)
):
    try:
        response = candidate_evaluation_service_object.get_suitable_candidates(
            requisition_id, request, db
        )
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get("/get-recommended-candidates/{requisition_id}")
def get_recommended_candidates(
    requisition_id: int, request: Request, db: Session = Depends(get_db)
):
    try:
        response = candidate_evaluation_service_object.get_recommended_candidates(
            requisition_id, request, db
        )
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get("/get-recommended-candidate/{recommended_candidate_id}")
def get_recommended_candidate(
    recommended_candidate_id: int, request: Request, db: Session = Depends(get_db)
):
    try:
        response = candidate_evaluation_service_object.get_recommended_candidate(
            recommended_candidate_id, db
        )
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.get("/get-candidate/{interview_feedback_id}")
def get_candidate(
    interview_feedback_id: int, request: Request, db: Session = Depends(get_db)
):
    try:
        response = candidate_evaluation_service_object.get_candidate(
            interview_feedback_id, db
        )
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post(
    "/take-action-on-profile",
    response_model=ApiSuccessResponse[ShorlistedCandidateResponse],
)
def take_action_on_profile(
    interview_feedback_input: ShortlistedCandidateCreate,
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
):
    try:
        response = candidate_evaluation_service_object.take_action_on_profile(
            interview_feedback_input, request, background_tasks, db
        )
        return ApiSuccessResponse(message="Success", data=response)
    except EntityNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/action-on-shortlisted-candidate/{shortlisted_candidate_id}")
def action_on_shortlisted_candidate(
    shortlisted_candidate_id: int,
    payload: ShortlistedCandidateAction,
    db: Session = Depends(get_db),
):
    try:
        response = candidate_evaluation_service_object.action_on_shortlisted_candidate(
            shortlisted_candidate_id, payload, db
        )
        return ApiSuccessResponse(
            message="Candidate action saved successfully", data=response
        )
    except EntityNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/get-shorlisted-candidates",
)
def get_shortlisted_candidates(request: Request, db: Session = Depends(get_db)):
    try:
        response = candidate_evaluation_service_object.get_shortlisted_candidates(
            request, db
        )
        return ApiSuccessResponse(message="Success", data=response)
    except EntityNotFoundException as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/create-activity", response_model=ApiSuccessResponse[CandidateActivityBase])
def create_activity(
    activity: CandidateActivityCreate, request: Request, db: Session = Depends(get_db)
):
    try:
        response = candidate_activity_service_object.create_activity(activity, request, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.get("/get-public-candidate/{recommended_candidate_hash}")
def get_public_candidate(
    recommended_candidate_hash: str, request: Request, db: Session = Depends(get_db)
):
    try:
        decode_hash = hash_id.decode(recommended_candidate_hash)
        interview_feedback_id = decode_hash[0] if decode_hash else None
        
        response = candidate_evaluation_service_object.get_candidate(
            interview_feedback_id, db
        )
        # Will utlize this if we need to get the recommended candidate
        # response = candidate_evaluation_service_object.get_recommended_candidate(
        #     recommended_candidate_id, db
        # )
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.get("/create-public-candidate-link/{recommended_candidate_id}")
def create_public_candidate_link(
    recommended_candidate_id: int, request: Request, db: Session = Depends(get_db)
):
    try:
        return ApiSuccessResponse(
            message="Success",
            data={
                "public_link": f"{RECRUITER_APP_HOST}/{hash_id.encode(recommended_candidate_id)}"
            }
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
    
@router.get("/get-public-non-interviewed-candidates/{recommended_candidate_hash}")
def get_public_candidate(
    recommended_candidate_hash: str, request: Request, db: Session = Depends(get_db)
):
    try:
        decode_hash = hash_id.decode(recommended_candidate_hash)
        candidate_id = decode_hash[0] if decode_hash else None
        response = UserServiceClass().get_user_detail(candidate_id, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.get("/get-searched-shortlisted-candidates")
def get_searched_shortlisted_candidates(request: Request, db: Session = Depends(get_db)):
    try:
        response = candidate_evaluation_service_object.get_searched_shortlisted_candidates(request, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
