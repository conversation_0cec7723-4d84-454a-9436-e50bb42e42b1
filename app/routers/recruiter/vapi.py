from fastapi import APIRouter, Depends, status, HTTPException, Request
from database import get_db
from sqlalchemy.orm import Session
from ..common_imports import *
from schemas.vapi import ConversationRequest, UpdateJobRequirementRequest,JobRequirementSchema,UpdateRecruiterInfo
from services.recruiter.vapi_recruiter_onboarding import RecruiterOnboardingServiceClass
from fastapi.encoders import jsonable_encoder
import logging

logger = logging.getLogger(__name__)

recruiter_onboarding_service = RecruiterOnboardingServiceClass()

router = APIRouter()




@router.post("/insert_transcript")
async def transcript_insertion (
    request: Request,
    conversation_request: ConversationRequest,
    db: Session = Depends(get_db),
):
    try:
        transcript = jsonable_encoder(conversation_request.transcript)
        tool_calls = jsonable_encoder(conversation_request.tool_calls)
        response = recruiter_onboarding_service.insert_recruiter_details_into_open_search(transcript, tool_calls)
        return ApiSuccessResponse(message="Recruiter info extracted", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
    

@router.post("/update_transcript")
async def update_transcript(
    request: Request,
    update_request: UpdateJobRequirementRequest,
    db: Session = Depends(get_db),
):
    try:
        document_id = update_request.hash
        updated_data = jsonable_encoder(update_request.job_requirements)
        recruiter_info=jsonable_encoder(update_request.recruiter_info)


        response = recruiter_onboarding_service.update_recruiter_details_into_open_search(document_id, updated_data,recruiter_info,db)

        return ApiSuccessResponse(message="Recruiter info updated successfully", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
    

@router.post("/update_recruiter_info")
async def update_recruiter_info(
    request: Request,
    update_request: UpdateRecruiterInfo,
    db: Session = Depends(get_db),
):
    try:
        document_id = update_request.hash
        recruiter_info=jsonable_encoder(update_request.recruiter_info)

        response = recruiter_onboarding_service.update_recruiter_info_into_open_search(document_id,recruiter_info)

        return ApiSuccessResponse(message="Recruiter info updated successfully", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
    

@router.post("/map_requistion")
async def map_req(
    request: Request,
    parsed_data: JobRequirementSchema,
):
    try:

        response = recruiter_onboarding_service.map_recruiter_requisition_details(parsed_data)

        return {
            "message": "Recruiter info updated successfully",
            "data": response
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )