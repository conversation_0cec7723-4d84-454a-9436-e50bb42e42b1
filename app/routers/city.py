from .common_imports import *
from services.city import CityServiceClass
from schemas.city_country import CityResponseSchema
import logging
from services.interview.interview_email import InterviewEmailServiceClass
from database import get_mongodb_client
from celery_tasks.interview_email_tasks import testing_task
from celery_tasks.scrapping_tasks import scrapping_dog_jobs_to_mongo
from celery_tasks.onboarding_tasks import send_cold_email_to_recruiter
from services.requisition.requisition_similarity import RequisitionSimilarity
logger = logging.getLogger(__name__)

router = APIRouter()

city_service_object = CityServiceClass()

#  response_model=ApiSuccessResponse[CityResponseSchema]
@router.get("/cities")
def get_cities(
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        response = city_service_object.get_cities(request, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

# Will remove this route once testing complete
@router.get("/testing")
def testing(request: Request, db: Session = Depends(get_db)):
    try:
        return False
        # return int(request.query_params.get("per_page", 10))
        # return RequisitionSimilarity().store_embeddings(2, db)
        return RequisitionSimilarity().get_similar_requisitions(82, db)
        RequisitionSimilarity().store_embeddings(1, db)
        # return RequisitionSimilarity().store_embeddings(82, db)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


# Will remove this route once testing complete
@router.get("/job-dump-test")
def get_jobs(request: Request, db: Session = Depends(get_mongodb_client)):
    try:
        scrapping_dog_jobs_to_mongo.delay()
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


from schemas.auth import ForgetPasswordRequest

@router.post("/send-test-email")
def send_test_email(payload: ForgetPasswordRequest, request: Request):
    try:
        from services.notification.email import (
            send_email_with_celery,
            send_email_background,
        )
        from dependencies import RECRUITER_OUTREACH_MAIL_FROM, INTERVIEW_SENDER_EMAIL

        res = send_email_with_celery(
            "Thank you for signing up with Vettio",
            payload.email,
            {"data": "Test Data"},
            "testing.html",
            INTERVIEW_SENDER_EMAIL,
            # {'<EMAIL>'}
        )
        return res
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
