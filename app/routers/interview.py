from .common_imports import *
from services.interview.interview import InterviewServiceClass
from services.interview.Intro_video import IntroVideoServiceClass
from schemas.question_feedback import QuestionFeedbackBase, QuestionFeedback,UpdateQuestionFeedback
from schemas.interview import Interview, UpdateInterviewStatus, UserId, UpdateInterviewStateMachineNode
from schemas.feedback import FeedbackCreateSchema, FeedbackSchema
from services.interview.candidate_picture import VideoProcessor
from services.external.openai import OpenAIServiceClass
from models.interview_feedback import InterviewFeedback
from celery_tasks.post_interview_process_tasks import interview_post_process
from models.interview import Interview as InterviewModel
import json
import logging


logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/interview", tags=["Interview"], dependencies=[Depends(getCurrentUser)]
)


interview_service_object = InterviewServiceClass()
intro_video_service_object = IntroVideoServiceClass()
candidate_pic_object = VideoProcessor()
open_ai_service_object = OpenAIServiceClass()


@router.post(
    "/store-question-feedback", response_model=ApiSuccessResponse[QuestionFeedback]
)
def store_question_feedback(
    request: Request,
    question_feedback: QuestionFeedbackBase,
    db: Session = Depends(get_db),
):
    try:
        response = interview_service_object.store_question_feedback(
            question_feedback, request, db
        )
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
    
@router.post(
    "/update-question-feedback", response_model=ApiSuccessResponse[QuestionFeedback]
)
def update_question_feedback(
    request: Request,
    payload: UpdateQuestionFeedback,
    db: Session = Depends(get_db),
):
    try:
        response = interview_service_object.update_question_feedback(
            payload, request, db
        )
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/get-question-feedback"
)
def get_question_feedback(
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        response = interview_service_object.get_question_feedback(request, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


#
@router.post("/update-interview-status", response_model=ApiSuccessResponse[Interview])
def update_interview_status(
    inputData: UpdateInterviewStatus,
    background_tasks: BackgroundTasks,
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        interview = interview_service_object.update_interview_status(
            inputData.interview_id, inputData.status, background_tasks, request, db
        )

        if interview.status == InterviewModel.INTERVIEW_COMPLETED:
            logger.info("Dispatching feedback job to background")
            interview_post_process.delay(request.state.user.id, interview.id)

        return ApiSuccessResponse(message="Success", data=interview)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/get-candidate-pic")
def get_candidate_pic(
    inputData: UserId,
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        response = candidate_pic_object.get_interview_videos_with_thumbnails(
            inputData.interview_id, inputData.user_id, request, db)

        return response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/evaluate/{interview_id}")
def evaluate(
    request: Request,
    interview_id: int,
    db: Session = Depends(get_db),
):
    try:
        interview = interview_service_object.get_interview_by_id(
            interview_id, db)
        response = OpenAIServiceClass().summarize_interview(interview, db)
        logger.info(
            f"Evaluation response for interview ID '{interview_id}': {response}")
        interview_feedback = db.query(InterviewFeedback).filter(
            InterviewFeedback.interview_id == interview_id).first()
        interview_feedback.score = response['evaluation']['percentage']
        interview_feedback.feedback = json.dumps(response)
        db.commit()
        db.refresh(interview_feedback)
        logger.info(
            f"Evaluated and updated interview feedback for interview ID '{interview_id}'")
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        logger.error(
            f"Error evaluating and updating interview feedback for interview ID '{interview_id}': {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get("/summarize-questions/{interview_id}")
def summarize_questions(
    interview_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
):
    try:
        logger.info(f"Summarizing questions for interview ID '{interview_id}'")
        background_tasks.add_task(
            interview_service_object.summarize_interview_questions, interview_id, db)

        logger.info(f"Summarized questions for interview ID '{interview_id}'")

        return True
    except Exception as e:
        logger.error(
            f"Error summarizing questions for interview ID '{interview_id}': {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/check-ai-generated-content-percentage/{interview_id}")
def check_aigeneratedcontentpercentage(
    interview_id: int,
    db: Session = Depends(get_db),
):
    try:
        interview = interview_service_object.get_interview_by_id(
            interview_id, db)
        response = open_ai_service_object.check_ai_generated_content(
            interview, db)
        return ApiSuccessResponse(message="Success", data=response)
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, 
            detail=str(e)
        )

@router.post("/update-interview-state-machine-nodes")
def update_interview_state_machine_nodes(
    inputData: UpdateInterviewStateMachineNode,
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        response = interview_service_object.update_interview_state_machine_nodes(inputData, request, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
    

@router.post("/store-feedback", response_model=ApiSuccessResponse[FeedbackSchema])
def store_feedback(
    request: Request,
    feedback: FeedbackCreateSchema,
    db: Session = Depends(get_db),
):
    try:
        response = interview_service_object.store_feedback(feedback, request, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )   
    
@router.get("/get-feedback/{interview_feedback_id}", response_model=ApiSuccessResponse[FeedbackSchema])
def get_feedback(
    interview_feedback_id: int,
    request: Request,   
    db: Session = Depends(get_db),
):
    try:
        response = interview_service_object.get_feedback(interview_feedback_id,request, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
