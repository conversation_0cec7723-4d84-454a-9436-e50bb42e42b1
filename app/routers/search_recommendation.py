from fastapi import Body
from services.external.openai import OpenAIServiceClass
from .common_imports import *
from fastapi import APIRouter, status, HTTPException
import logging
from services.search_recommendation import SearchRecommendationService
from fastapi import Query
from database import get_db  # Import the get_db function
logger = logging.getLogger(__name__)
from sqlalchemy.orm import Session
from typing import Dict
import logging

router = APIRouter()

@router.get("/search-candidates")
def determine_context(
    query: str = Query(...),
    level: str = Query(...),
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    most_recent: bool = Query(...),
    db: Session = Depends(get_db),
    city_id: int = Query(0),
    country_id: int = Query(0),
    state_id: int = Query(0)
):
    try:
        response = SearchRecommendationService().get_similarity_search(
            query=query, db=db, level=level, page=page, per_page=per_page, most_recent=most_recent, city_id=city_id, state_id=state_id, country_id=country_id
        )
        return ApiSuccessResponse(message="Success", data=response)

    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))



@router.post("/get-matched-filters")
def match_filters(
    user_id: int = Query(0),
    filters: Dict = Body(...),
    db: Session = Depends(get_db)
):
    try:
        response = SearchRecommendationService().get_matched_filters(
            user_id=user_id, filters=filters
        )
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))