from fastapi import APIRouter, Depends, status, HTTPException, Request
from database import get_db
from sqlalchemy.orm import Session
from schemas.requisition import (
    RequsitionResponseSchema,
    UpdateFineTuningStatusSchema,
)
from typing import List
from schemas.question_feedback import QuestionFeedbackBase
from schemas.question_feedback import QuestionFeedback
from schemas.interview import UpdateInterviewStateMachineNode
from services.user.user import UserServiceClass
from services.user.user_interview import UserInterviewServiceClass
from services.user.user_requisition import UserRequisitionServiceClass
from .common_imports import *
from services.requisition.evaluation_criteria import EvaluationCriteria
from services.external.openai import OpenAIServiceClass
from services.interview.pre_evaluate import PreEvaluate
from services.requisition.recommendation import CandidateRecommendation
from services.interview.interview import InterviewServiceClass
from services.requisition.requisition import RequisitionServiceClass
from services.requisition.requisition_scrapper import RequsisitionScraperService
from services.parser.requisition_parser import RequisitionParserServiceClass
from fastapi_pagination import Page, add_pagination
from helper.helper import get_authenticate_user
import logging
logger = logging.getLogger(__name__)

router = APIRouter()
requsition_service = RequisitionServiceClass()
requisition_parser_service = RequisitionParserServiceClass()
open_ai_service_object = OpenAIServiceClass()


router = APIRouter(prefix="/requisition", tags=["Requisitions"])


@router.post("/scrape")
def scrape_jobs(
        request: Request,
        background_tasks: BackgroundTasks,
        db: Session = Depends(get_db)):
    try:
        print(db)
        req_scrapper = RequsisitionScraperService(db)
        response = req_scrapper.run_scraping_service(request, background_tasks)
        return response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(db))


# , params: Params = Depends()
# , response_model=ApiSuccessResponse[RequsitionsResponseSchema]
@router.get(
    "/requisitions"
)
async def get_requisition(request: Request, db: Session = Depends(get_db)):
    try:
        user = get_authenticate_user(request)
        requsitions = requsition_service.get_requisitions(request, db)
        return ApiSuccessResponse(
            message="Requisitions fetched successfully",
            data=requsitions,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.put("/update-requisition", response_model=ApiSuccessResponse[List[RequsitionResponseSchema]])
async def updateRequsitionFineTuningStatus(request: UpdateFineTuningStatusSchema, db: Session = Depends(get_db)):
    try:
        response = requsition_service.update_requisitions_fine_tuning_status(
            request.requisition_ids, db)
        return ApiSuccessResponse(message="Requisitions updated successfully", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

add_pagination(router)


@router.get(
    "/get-requisition/{id}", response_model=ApiSuccessResponse[RequsitionResponseSchema]
)
def get_requisition(id: int, request: Request, db: Session = Depends(get_db)):
    try:
        user = get_authenticate_user(request)
        response = requsition_service.get_requisition(request, id, db)
        return ApiSuccessResponse(
            message="Requisitions fetch successfully", data=response
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post("/parse-requisition",)
def parse_requisition(input_data: dict, request: Request):
    try:
        response = requisition_parser_service.parse_static_req(
            input_data["description"])
        return response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/pre-evaluate/{requisition_id}")
def pre_evaluate(
    requisition_id: int,
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
):
    try:
        logger.info(
            f"Pre-evaluating user ID: '{request.state.user.id}', requisition ID: '{requisition_id}'")

        background_tasks.add_task(
            PreEvaluate().evaluate_candidate, requisition_id, request, db
        )

        logger.info(
            f"Pre-evaluated user ID: '{request.state.user.id}', requisition ID: '{requisition_id}'")

        return True
    except Exception as e:
        logger.error(
            f"Error pre-evaluating user ID: '{request.state.user.id}', requisition ID: '{requisition_id}': {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/pre-evaluate-job-requisition/{requisition_id}",)
def pre_evaluate_job_requisition(
    requisition_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    try:
        background_tasks.add_task(
            PreEvaluate().evaluate_by_requisition_id, requisition_id, db)
        return True
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/create-recommendations/{requisition_id}")
def create_recommendations(
    requisition_id: int,
    db: Session = Depends(get_db)
):
    try:
        return False
        task_ids = CandidateRecommendation().create_recommendations(
            requisition_id, db)

        return task_ids
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/get-recommendations/{requisition_id}")
def get_recommendations(
    requisition_id: int,
    db: Session = Depends(get_db)
):
    try:
        response = CandidateRecommendation().get_recommendations(requisition_id, db)

        return response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/create-evaluation-criteria/{requisition_id}",)
def create_evaluation_criteria(
    requisition_id: int,
    db: Session = Depends(get_db)
):
    try:
        response = EvaluationCriteria().create_evaluation_criteria(requisition_id, db)

        return response
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post(
    "/store-question-feedback-no-auth", response_model=ApiSuccessResponse[QuestionFeedback]
)
def store_question_feedback_no_auth(
    question_feedback: QuestionFeedbackBase,
    db: Session = Depends(get_db),
):
    try:
        logger.info("Storing question feedback without authentication")
        response = InterviewServiceClass().store_question_feedback_no_auth(
            question_feedback, db
        )
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        logger.error(
            f"Error storing question feedback without authentication: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post("/update-interview-state-machine-nodes-no-auth")
def update_interview_state_machine_nodes_no_auth(
    inputData: UpdateInterviewStateMachineNode,
    db: Session = Depends(get_db),
):
    try:
        response = InterviewServiceClass().update_interview_state_machine_nodes_no_auth(
            inputData, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get("/get-resume-no-auth/{user_id}")
async def get_resume_no_auth(user_id: int, db: Session = Depends(get_db)):
    try:
        user = UserServiceClass().get_user_detail(user_id, db)
        user_recent_interview = UserInterviewServiceClass().get_recent_user_interview_no_auth(
            user_id, db
        )
        user["recent_interview_status"] = (
            user_recent_interview["status"] if user_recent_interview else None
        )
        return ApiSuccessResponse(message="Success", data=user)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/get-user-requisition-no-auth/{user_id}"
)
def get_user_requisition_no_auth(
    user_id: int, db: Session = Depends(get_db)
):
    try:
        response = UserRequisitionServiceClass().get_user_requisition_no_auth(
            user_id, db
        )
        return ApiSuccessResponse(
            message="Requisitions fetch successfully", data=response
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
