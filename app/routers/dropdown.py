from .common_imports import *
from services.dropdown import DropDownServiceClass
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/dropdown", tags=["Dropdown"])

dropdown_service_object = DropDownServiceClass()

@router.get("/get-countries")
def get_cities(
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        response = dropdown_service_object.get_countries_for_dropdown(db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.get("/get-country-states/{country_id}")
def get_country_states(
    country_id: int,
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        response = dropdown_service_object.get_country_states(country_id, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )

@router.get("/get-state-cities/{state_id}")
def get_state_cities(
    state_id: int,
    request: Request,
    db: Session = Depends(get_db),
):
    try:
        response = dropdown_service_object.get_state_cities(state_id, db)
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
    
@router.get("/get-canidate-countries/{requisition_id}")
def get_suitable_candidates(
    requisition_id: int, request: Request, db: Session = Depends(get_db)
):
    try:
        response = dropdown_service_object.get_suitable_candidates_countries(
            requisition_id, request, db
        )
        return ApiSuccessResponse(message="Success", data=response)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
