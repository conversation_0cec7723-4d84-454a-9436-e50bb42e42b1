from pydantic import BaseModel, Field
from typing import List
from datetime import date


class Education(BaseModel):
   school: str = Field(description="School name.")
   degree: str = Field(description="Degree name.")
   field: str = Field(description="Field of study.")
   description: str = Field(
       description="Exact education description mentioned in the resume.")
   grade: str = Field(description="Education grade (CGPA or percentage.)")
   city: str = Field(description="City name (if available)", default="")
   country: str = Field(description="Country name (if available)", default="")
   start_date: date = Field(
       description="Start date of education in YYYY-MM-DD (if available).")
   end_date: str = Field(
       description="End date of education in YYYY-MM-DD (if available). NULL IF CURRENTLY STUDYING.")




class Project(BaseModel):
   name: str = Field(description="Project name.")
   description: str = Field(
       description="Exact and detailed project description mentioned in the resume.")
   duration: str = Field(
       description="Project duration in terms of years and/or months.")


class WorkExperience(BaseModel):
   organization: str = Field(description="Organization/company name.")
   job_title: str = Field(description="Job title.")
   emp_type: str = Field(
       description="Employee type (part-time, full-time, or contract.)")
   location_type: str = Field(
       description="Location type (onsite, remote, or hybrid.)")
   description: str = Field(
       description="Exact and detailed work experience description mentioned in the resume.")
   start_date: date = Field(
       description="Start date of work experience in YYYY-MM-DD (if available)")
   end_date: str = Field(
       description="End date of work experience in YYYY-MM-DD (if available). NULL IF CURRENTLY WORKING.")
   city: str = Field(description="City name of organization/company (if available)", default="")
   country: str = Field(description="Country name of organization/company (if available)", default="")




class Address(BaseModel):
   street: str = Field(description="Street name.")
   city: str = Field(description="Full descriptive name of the city where the user lives (residential city), from the resume", default="null")
   state: str = Field(description="State name.", default="null")
   zip: str = Field(description="Zip code.")
   country: str = Field(description="Code of the country where the user lives (residential country)", default="null")




class Resume(BaseModel):
   name: str = Field(
       description="Exact name of the person mentioned in the resume.")
   email: str = Field(
       description="Email address mentioned in the resume. NULL IF NOT PROVIDED.")
   total_experience: str = Field(
       description=f"Total work experience in terms of years and/or months. If the total work experience is not mentioned, calculate it by subtracting the start date of the first job from today.")
   skills: List[str] = Field(description="List of skills.")
   tools: List[str] = Field(description="List of tools.")
   education: List[Education] = Field(description="List of education.")
   certifications: List[str] = Field(description="List of certifications.")
   projects: List[Project] = Field(description="List of projects.")
   work_experience: List[WorkExperience] = Field(
       description="List of work experience.")
   linkedin: str = Field(
       description="LinkedIn profile URL mentioned in the resume. NULL IF NOT PROVIDED.")
   number: str = Field(
       description="Phone number mentioned in the resume. NULL IF NOT PROVIDED.")
   address: Address = Field(
       description="Address in terms of street, city , state, zip, country.")
   summary: str = Field(
       description="Resume summary. GENERATE IF NOT PROVIDED.")
