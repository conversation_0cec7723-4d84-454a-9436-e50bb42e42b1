from pydantic import BaseModel
from typing import Optional


class ShortlistedCandidateSchema(BaseModel):
    interview_id: int
    requisition_id: int
    candidate_id: int
    recruiter_id: int
    feedback: Optional[str] = None
    status: int

    class Config:
        orm_mode = True


class ShortlistedCandidateCreate(BaseModel):
    interview_feedback_id: int
    status: int
    feedback: Optional[str] = None
    user_id: Optional[int] = None

    class Config:
        orm_mode = True

class ShortlistedCandidateAction(BaseModel):
    status: int
    
class ShorlistedCandidateResponse(ShortlistedCandidateSchema):
    pass