from typing import Union, Optional, List, Literal
from pydantic import BaseModel, Field


class Filters(BaseModel):
    city: Optional[List[str]] = Field(
        default=None,
        description="City or cities mentioned in the query, as a comma-separated string if multiple. In case of region or continent, most common countries in that region or continent"
    )
    country: Optional[List[str]] = Field(
        default=None,
        description="Country or countries mentioned in the query, as a comma-separated string if multiple"
    )
    state: Optional[List[str]] = Field(
        default=None,
        description="State or states mentioned in the query, as a comma-separated string if multiple"
    )

    exp_greater_than_or_equals: Optional[int] = Field(
        default=None,
        description="Minimum years of experience specified in the user query, if any"
    )
    exp_less_than_or_equals: Optional[int] = Field(
        default=None,
        description="Maximum years of experience specified in the user query, if any"
    )

    organization: Optional[str] = Field(
        default=None,
        description="Organization name mentioned in the query, if any"
    )
    industry: Optional[str] = Field(
        default=None,
        description="Industry mentioned in the query, if any"
    )
    location_preferences: Optional[str] = Field(
        default=None,
        description="Preferred work setup: onsite, remote, hybrid, or all of the above"
    )
    expected_salary: Optional[str] = Field(
        default=None,
        description="Expected salary if mentioned"
    )
    notice_period: Optional[str] = Field(
        default=None,
        description="Notice period if mentioned (in total number of days)"
    )


from pydantic import BaseModel, Field
class Pins(BaseModel):
    job_title: str = Field(
        description="Job title mentioned in the query, comma-separated if multiple"
    )
    companies: str = Field(
        description="Companies mentioned in the query, comma-separated if multiple"
    )
    education: str = Field(
        description="Educational qualifications or institutions mentioned"
    )
    location: str = Field(
        description="Cities, states, or countries mentioned in the query, comma-separated if multiple"
    )
    skills: str = Field(
        description="Skills or worded experience mentioned in the query, comma-separated if multiple"
    )
    experience: str = Field(
        description="Duration of experience in terms of years mentioned in query"
    )
    industry: str = Field(
        description="Industries mentioned in the query, comma-separated if multiple"
    )

class QueryContextClassification(BaseModel):
    rephrased_query: str = Field(
        ...,  
        description="Query rewritten to include only skill and experience context, excluding companies, years, or locations"
    )
    candidate_skills: bool = Field(
        ...,  
        description="True if the query mentions any skills (technical or soft). Otherwise False"
    )
    work_experience: bool = Field(
        ...,  
        description="True if the query mentions any work experience details like roles, job titles, responsibilities, or projects. Otherwise False"
    )
    filters: Filters
    pins: Pins

class EvaluatedFilter(BaseModel):
    filter_key: str = Field(description="Filter key from the search filters")
    
    value: Union[str, int, float, bool, List[Union[str, int, float]]] = Field(
        description="Matched value from candidate data; None if no relevant match"
    )
    match_status: Literal["present", "partially_present", "absent", "inferred"] = Field(
        description="Match status between filter and candidate data"
    )
    reason: str = Field(description="Explanation of the match status")

class TotalEvaluatedFilters(BaseModel):
    evaluated_filters: List[EvaluatedFilter] = Field(
        description="List of all evaluated filters with their status and reasoning"
    )
