from pydantic import BaseModel, EmailStr, validator, root_validator, validator
from typing import Optional, ClassVar


class ForgetPasswordRequest(BaseModel):
    email: EmailStr
class EmailRequest(BaseModel):
    email: EmailStr

class AccessTokenRequest(BaseModel):
    token: str
    
class ResetPasswordRequest(BaseModel):
    token: str
    password: str
    confirm_password: str
    
    
    MIN_PASSWORD_LENGTH: ClassVar[int] = 8
    @validator('password')
    def validate_password(cls, value):
        if not value.strip():
            raise ValueError('Password is required')
        if len(value) < cls.MIN_PASSWORD_LENGTH:
            raise ValueError(f'Password must be at least {cls.MIN_PASSWORD_LENGTH} characters long')
        return value

    @validator('confirm_password')
    def validate_confirm_password(cls, value, values):
        password = values.get('password')
        if not value.strip():
            raise ValueError('Confirm password is required')
        if password and value != password:
            raise ValueError('Passwords do not match')
        return value

class ForgetPasswordResponse(BaseModel):
    email: str
    
class ValidateRecruiter(BaseModel):
    token: str
    # password: str