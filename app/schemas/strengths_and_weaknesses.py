from pydantic import BaseModel, Field
from typing import List
from schemas.requirements_and_skills import Skill


class StrengthsAndWeaknesses(BaseModel):
    strengths: List[str] = Field(
        description="A list of strings representing the candidate's strengths in detail.")
    weaknesses: List[str] = Field(
        description="A list of strings representing the candidate's weaknesses in detail.")
    skills: List[Skill] = Field(description="A list of evaluated skills.")
    overview: str = Field(
        description="Assess how well the candidate fits the job requisition in terms of requirements, and skills. Provide a detailed comment on their suitability in bullet points.")
    is_overqualified: bool = Field(
        description="True if the candidate is overqualified for the job, otherwise False.", default=False)
