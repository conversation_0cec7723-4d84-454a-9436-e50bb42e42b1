from pydantic import BaseModel,Field
from schemas.organization import OrganizationSchema
from schemas.city_country import CityCountrySchema
from typing import Optional, List
from datetime import date

class RequisitionSchema(BaseModel):
    id: int
    user_id: Optional[int]
    title: str
    description : str
    salary_range : Optional[str] 
    location_type : Optional[str]
    job_type : Optional[str]
    close_date : Optional[date] 
    status : int
    job_link : Optional[str]
    level: str
    fine_tuning_status : Optional[int] = 0
    profession_id: Optional[int]
    # requirements: Optional[str]
    # responsibilities: Optional[str]
    llm_requistion_details: Optional[str]
    binary_requirements: Optional[str]
    salary_details: Optional[str]
    long_description: Optional[str]
    show_salary: Optional[int] = 0

    class Config:
        orm_mode = True


class RequsitionResponseSchema(RequisitionSchema):
    organization: OrganizationSchema
    city: Optional[CityCountrySchema] = None
    user_requisition: Optional[dict] = None
    user_count: Optional[int] = 0

    class Config:
        from_attributes = True

class UserRequisitionSchema(RequisitionSchema):
    organization: OrganizationSchema
    city: Optional[CityCountrySchema] = None
    class Config:
        from_attributes = True

class RequisitionListSchema(UserRequisitionSchema):
    user_count: Optional[int] = 0
    class Config:
        from_attributes = True

class RequsitionsResponseSchema(BaseModel):
    pagination: Optional[dict]
    items: List[RequisitionListSchema] 

class UpdateFineTuningStatusSchema(BaseModel):
    requisition_ids : List[int]


class RequisitionID(BaseModel):
    requisition_id:int

class CandidateEvaluationPayload(BaseModel):
    user_id: int
    requisition_id:int


class ReadSchema(RequisitionSchema):
    city_id : Optional[int]
    organization_id : Optional[int]
class RequisitionCreateSchema(BaseModel):
    title: str
    description : Optional[str] = None
    salary_range : Optional[str] = None
    location_type : Optional[str] = None
    job_type : Optional[str] = None
    close_date : Optional[date] = None
    status : Optional[int] = None
    job_link : Optional[str] = None
    level: Optional[str] = None
    fine_tuning_status : Optional[int] = None
    profession_id: Optional[int] = None
    # requirements: Optional[str] = None
    # responsibilities: Optional[str] = None
    salary_details: Optional[str] = None
    city_id : Optional[int] = None
    state_id : Optional[int] = None
    country_id : Optional[int] = None
    long_description: Optional[str] = None
    show_salary: Optional[bool] = False
    llm_requistion_details: Optional[list[str]]= None
    # organization_id : Optional[int]= None


class RequisitionRequirementsSchema(BaseModel):
    mandatory_requirements: Optional[List[str]] = Field(description="List of mandatory requirements for the requisition")
    preferred_requirements: Optional[List[str]] = Field(description="List of preferred requirements for the requisition")

class RequisitionRequirement(BaseModel):
    requirement: str = Field(description="The specific requirement being evaluated")
    evaluation: str = Field(description="The evaluation of the candidate against the requirement")
    status: str = Field(description="Status of the evaluation, e.g., 'met', 'not met', 'partially met','not fully met','not confirmed','strongly met'")

class RequisitionRequirementsEvaluationSchema(BaseModel):
    evaluation: List[RequisitionRequirement] = Field(
        description="List of evaluations for each requirement in the requisition")
