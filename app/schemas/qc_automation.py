from pydantic import BaseModel, Field
from typing import List
from datetime import date

class Education(BaseModel):
   school: str = Field(description="School name.")
   degree: str = Field(description="Degree name.")
   field: str = Field(description="Field of study.")
   description: str = Field(
       description="Exact education description mentioned in the resume.")
   from_date: date = Field(
       description="Start date of education in YYYY-MM-DD (if available).")
   to_date: str = Field(
       description="End date of education in YYYY-MM-DD (if available). EMPTY STRING IF CURRENTLY STUDYING.")
   flag: bool = Field(description="Boolean value. Set to True if null strings are present, otherwise False", default=False)
   flag_reason: str = Field(description="Reason as to why the flag is set as either True or False")

class WorkExperience(BaseModel):
   organization: str = Field(description="Organization/company name.")
   job_title: str = Field(description="Job title.")
   emp_type: str = Field(
       description="Employee type from the JSON object")
   description: str = Field(
       description="Exact and detailed work experience description mentioned in the resume.")
   from_date: date = Field(
       description="Start date of work experience in YYYY-MM-DD (if available)")
   to_date: str = Field(
       description="End date of work experience in YYYY-MM-DD (if available). EMPTY STRING IF CURRENTLY STUDYING.")
   flag: bool = Field(description="Boolean value. Set to True if null strings are present, otherwise False", default=False)
   flag_reason: str = Field(description="Reason as to why the flag is set as either True or False")


class UserDetails(BaseModel):
   first_name: str = Field(
       description="First name of the person from the JSON Object")
   last_name: str = Field(description="Last name of the person from the JSON Object")
   expected_salary: str = Field(description="Expected salary from the JSON Object")
   location_preference: str = Field(description="Location preferences of the user from the JSON Object")
   notice_period: str = Field(description="Notice period of user from the JSON Object")
   email: str = Field(
       description="Email address from the JSON Object")
   total_experience: str = Field(
       description=f"Total work experience from the JSON Object")
   education: List[Education] = Field(description="List of education.")
   phone: str = Field(
       description="Phone number from the JSON Object")
   location: str = Field(
       description="Location of the user from the JSON Object")
   flag: bool = Field(description="Flag set to True if there are any typos or null strings, otherwise False", default=False)
   flag_reason: str = Field(description="Reason as to why the flag is set as either True or False")
class UserExperienceInfo(BaseModel):
    work_experience: List[WorkExperience] = Field(
       description="List of work experience.")
    

class UserExperienceInfoFlags(BaseModel):
    flags: List[bool] = Field(description="Flag set to True if there are any typos or null strings, otherwise False", default=False)

class UserEducationInfoFlags(BaseModel):
    flags: List[bool] = Field(description="Flag set to True if there are any typos or null strings, otherwise False", default=False)


class InterviewTranscript(BaseModel):
    question_id: str = Field(description="ID of the question/answer pair")
    flag: bool = Field(description="True if the question/answer pair is irrelevant or filler, otherwise False", default=False)
    flag_reason: str = Field(description="Reason as to why the flag is set as either True or False")

class FlaggedData(BaseModel): 
    data: List[InterviewTranscript]




class Requirement(BaseModel):
    requirement: str = Field(...,
        description="Represents the specific requirement from the requisition.")
    evaluation: str = Field(...,
        description="A detailed assessment of the candidate's proficiency in that requirement, focusing on:\n- Their experience level.\n- Quantifiable achievements.\n- Any gaps or lack of articulation.")
    statement: str = Field(...,
        description="Exact evidence if any from the resume or interview by the candidate. Make the statement grammarly correct if necessary.")
    flag: bool = Field(
        description="A flag indicating whether there is an issue in the requirement or not. True if there is an issue, otherwise False", default=False
    )
    flag_reason: str = Field(description="Reason stating as to why you set the flag as True or False", default="")

class RequirementsListWithFlag(BaseModel):
    list_of_requirements_with_flags: List[Requirement] = Field(
        description="An array of objects for EACH requirement in the requisition, with a flag parameter for each requirement set to either True or False.")


class StrengthsAndWeaknesses(BaseModel):
    strengths : List[str] = Field(description="List of candidate's strengths with any corrections")
    strengths_flag: bool = Field(description="A flag set to True if there are any issues in the strengths, otherwise False", default=False)
    strength_flag_reason: str = Field(description="Reason stating as to why you set the strength flag as True or False")
    weaknesses : List[str] = Field(description="List of candidate's weaknesses with any corrections")
    weaknesses_flag: bool = Field(description="A flag set to True if there are any issues in the weaknesses, otherwise False", default=False)
    weakness_flag_reason: str = Field(description="Reason stating as to why you set the weakness flag as True or False")

class Overview(BaseModel):
    overview: List[str] = Field(description="Overview of the candidate (corrected, if necessary)")
    flag: bool = Field(description="Flag variable, set to True if there are any issues with the overview, otherwise set it to False.", default=False)
    flag_reason: str = Field(description="Reason stating as to why you set the flag as True or False")