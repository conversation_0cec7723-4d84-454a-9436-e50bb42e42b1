from pydantic import BaseModel, Field
from typing import List


class Requirement(BaseModel):
    evaluation: str = Field(
        description="A detailed assessment of the candidate's proficiency in the job requirement, focusing on:\n- Their experience level.\n- Quantifiable achievements.\n- Any gaps or lack of articulation.")
    statement: str = Field(
        description="Verbatim evidence from the interview or resume. Ensure grammatical correctness but retain the original context.")
    percentage: int = Field(
        description="A numerical score in range 0% to 100%, indicating how much the candidate has fulfilled the requirement based on the scoring guidelines.")


class Skill(BaseModel):
    skill: str = Field(
        description="Name of skill evaluated.")
    percentage: int = Field(
        description="A numerical score (0-100%) indicating how much the candidate has fulfilled the skill based on the evaluation. Score higher (more than 60%) if the candidate has demonstrated both specific examples and quantifiable achievements from his past experience, score average (less than 60% and more than 40%) if either of the specific examples or quantifiable achievements were provided, and score lower (less than 40%) if both specific examples and quantifiable achievements were not provided from his past experience."
    )


class RequirementsAndSkills(BaseModel):
    requirements: List[Requirement] = Field(
        description="An array of objects for EACH requirement in the requisition, except for location-based requirements")
    skills: List[Skill] = Field(
        description="An array of objects for each skill in the requisition.")
    relevant_experience: str = Field(
        description="Calculated relevant experience of the candidate in terms of years and months that strictly aligns with the job requisition."
    )
    overview: str = Field(
        description="Assess how well the candidate fits the job requisition in terms of requirements, and skills. Provide a detailed comment on their suitability in bullet points.")
    location_satisfied: bool = Field(
        description="True if the candidate's location preference matches the job location, otherwise False.", default=False)
    is_overqualified: bool = Field(
        description="True if the candidate is overqualified for the job, otherwise False.", default=False)
