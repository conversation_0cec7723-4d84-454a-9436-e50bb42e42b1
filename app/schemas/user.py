from pydantic import BaseModel, Email<PERSON>tr, validator, root_validator, validator
from email_validator import validate_email, EmailNotValidError
from typing import Optional, ClassVar, List
from datetime import date, datetime
from enum import Enum
import re
from fastapi import Form
from schemas.user_experience_skill import UserExperienceSkillBase
from schemas.user_education import UserEducation
from schemas.user_experience import UserExperience
from schemas.shortlisted_candidate import ShortlistedCandidateSchema

class LocationPreference(str, Enum):
    REMOTE = "Remote"
    ONSITE = "On-site"
    HYBRID = "Hybrid"
    ALLOFABOVE = "All of above"


class UserBaseMandatory(BaseModel):
    first_name: str
    last_name: str
    email: str


class UserBaseOptional(UserBaseMandatory):
    profession_id: Optional[int] = None
    phone: Optional[str] = None
    expected_salary: Optional[str] = None
    # make location_preferences an Enum and an array
    location_preferences: Optional[str] = None
    notice_period: Optional[str] = None
    nationality: Optional[str] = None
    date_of_birth: Optional[date] = None
    gender: Optional[str] = None
    city_id: Optional[int] = None
    profile_pic: Optional[str] = None
    resume_link: Optional[str] = None
    total_experience: Optional[str] = None
    # make status an Enum
    salary_negotiable: Optional[bool] = False
    status: Optional[int] = 0
    created_at: Optional[datetime] = None
    linkedin_url: Optional[str] = None  

class GuestUserCreate(BaseModel):
   email: str

   @validator("email")
   def validate_email(cls, value):
       if not value.strip():
           raise ValueError("Email is required")
       try:
           validate_email(value)
       except EmailNotValidError:
           raise ValueError("Invalid Email Address")
       return value


class User(UserBaseOptional):
    id: int
    access_token: Optional[str] = None

    class Config:
        from_attributes = True

class SuitableUser(UserBaseOptional):
    id: int
    access_token: Optional[str] = None
    shortlisted_candidate: Optional[List[ShortlistedCandidateSchema]] = None

    class Config:
        from_attributes = True

class UserCreateRevamp(BaseModel):
    email: str
    password: str
    confirm_password: str
    MIN_PASSWORD_LENGTH: ClassVar[int] = 8

    @validator("email")
    def validate_email(cls, value):
        if not value.strip():
            raise ValueError("Email is required")
        try:
            validate_email(value)
        except EmailNotValidError:
            raise ValueError("Invalid Email Address")
        return value

    @validator("password")
    def validate_password(cls, value):
        if not value.strip():
            raise ValueError("Password is required")
        if len(value) < cls.MIN_PASSWORD_LENGTH:
            raise ValueError(
                f"Password must be at least {cls.MIN_PASSWORD_LENGTH} characters long"
            )
        return value

    @validator("confirm_password")
    def validate_confirm_password(cls, value, values):
        password = values.get("password")
        if not value.strip():
            raise ValueError("Confirm password is required")
        if password and value != password:
            raise ValueError("Passwords do not match")
        return value

class UserCreate(UserBaseMandatory):
    phone: Optional[str] = None
    password: str
    confirm_password: str

    # Constants
    MIN_NAME_LENGTH: ClassVar[int] = 2
    MIN_PASSWORD_LENGTH: ClassVar[int] = 8

    @validator("first_name")
    def validate_first_name(cls, value):
        if not value.strip():
            raise ValueError("First name is required")
        if len(value) < cls.MIN_NAME_LENGTH:
            raise ValueError(
                f"First name must be at least {cls.MIN_NAME_LENGTH} characters long"
            )
        return value

    @validator("last_name")
    def validate_last_name(cls, value):
        if not value.strip():
            raise ValueError("Last name is required")
        if len(value) < cls.MIN_NAME_LENGTH:
            raise ValueError(
                f"Last name must be at least {cls.MIN_NAME_LENGTH} characters long"
            )
        return value

    @validator("email")
    def validate_email(cls, value):
        if not value.strip():
            raise ValueError("Email is required")
        try:
            validate_email(value)
        except EmailNotValidError:
            raise ValueError("Invalid Email Address")
        return value

    @validator("password")
    def validate_password(cls, value):
        if not value.strip():
            raise ValueError("Password is required")
        if len(value) < cls.MIN_PASSWORD_LENGTH:
            raise ValueError(
                f"Password must be at least {cls.MIN_PASSWORD_LENGTH} characters long"
            )
        return value

    @validator("confirm_password")
    def validate_confirm_password(cls, value, values):
        password = values.get("password")
        if not value.strip():
            raise ValueError("Confirm password is required")
        if password and value != password:
            raise ValueError("Passwords do not match")
        return value

    # class Config:
    #     extra = 'forbid'


class UserUpdate(UserCreate, UserBaseOptional):

    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None

    password: Optional[str] = None
    confirm_password: Optional[str] = None

    class Config:
        from_attributes = True


class DeleteUser(BaseModel):
    email: str


class UserDetail(User):
    educations: Optional[List[UserEducation]] = None
    skills: Optional[List[UserExperienceSkillBase]] = None
    experiences: Optional[List[UserExperience]] = None


class CompleteUserSignup(BaseModel):
    expected_salary: str
    location_preferences: Optional[str] = None

    # location_preferences: LocationPreference
    notice_period: str
    linkedin_url: Optional[str] = None

    @classmethod
    def as_form(
        cls,
        expected_salary: str = Form(...),
        location_preferences: str = Form(...),
        notice_period: str = Form(...),
        linkedin_url: Optional[str] = Form(None),
    ):
        return cls(
            expected_salary=expected_salary,
            location_preferences=location_preferences,
            notice_period=notice_period,
            linkedin_url=linkedin_url,
        )
        
class UpdateUserPreferences(BaseModel):
    expected_salary: str
    loc_preferences: str
    notice_period: str
    key: str
    value: str
    salary_negotiable: bool