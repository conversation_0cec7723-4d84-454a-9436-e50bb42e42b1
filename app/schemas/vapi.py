from typing import List, Optional
from pydantic import Field,BaseModel

class OnSiteHybridDetails(BaseModel):
    office_location: Optional[str] = Field(description="Exact office location (City, State/Area).")
    relocation_openness: Optional[str] = Field(description="Openness to candidates who would need to relocate.")
    international_candidate_openness: Optional[bool] = Field(description="Openness to considering international candidates (potentially requiring visa sponsorship).")

class SalaryDetails(BaseModel):
    salary_range: Optional[str]
    show_salary: Optional[bool]
    currency:Optional[str] = Field(description="Currency in which the salary is offered (e.g., USD, EUR).")

class FullyRemoteDetails(BaseModel):
    remote_restrictions: Optional[str] = Field(description="Any restrictions on where the remote employee can be based (e.g., specific states, countries, time zones).")

class OrganizationalContext(BaseModel):
    reporting_position: Optional[str] = Field(description="Position or title this role reports directly to.")
    has_direct_reports: Optional[bool] = Field(description="Whether this role has any direct reports (true/false).")
    direct_reports_count: Optional[str] = Field(description="Roughly how many direct reports this role will have.")
    team_size: Optional[str] = Field(description="Approximate size of the immediate team.")

class JobRequirementSchema(BaseModel):
    job_title: Optional[str] = Field(description="Title of the job position.")
    must_have_technical_skills: Optional[List[str]] = Field(description="Vital technical skills required for the role.")
    required_experience_years: Optional[str] = Field(description="Minimum or range of years of required experience.")
    specific_tools_technologies_certifications: Optional[List[str]] = Field(description="Specific tools, technologies, or certifications required.")
    
    location_requirements: Optional[str] = Field(description="Initial determination of work setup (On-site, Hybrid, or Fully Remote).")
    
    onsite_or_hybrid_details: OnSiteHybridDetails = Field(description="Details specific to On-site or Hybrid roles.")
    fully_remote_details: FullyRemoteDetails = Field(description="Details specific to Fully Remote roles.")

    organizational_context: OrganizationalContext = Field(description="Optional organizational context for the role.")
    
    negative_constraints: Optional[List[str]] = Field(description="Specific backgrounds or experiences generally not a good fit for this position.")
    role_pitch: Optional[str] = Field(description="What makes this role compelling or attractive to top candidates.")
    salary_details: SalaryDetails = Field(description="Salary details for the position.")
    company_description: Optional[str] = Field(description="A brief overview of the company to provide context to candidates.")
    preferred_previous_positions: Optional[List[str]] = Field(description="Specific job titles or types of roles held previously that are preferred for this position.")
    preferred_industries: Optional[List[str]] = Field(description="Industries ideally preferred for this candidate's experience.")

    

class RecruiterInfo(BaseModel):
    recruiter_first_name: Optional[str]
    recruiter_last_name: Optional[str]
    recruiter_email_address: Optional[str]
    recruiter_company_name: Optional[str]
    recruiter_contact:Optional[str]
    recruiter_city_id:Optional[int]
    recruiter_state_id:Optional[int]
    recruiter_country_id:Optional[int]


class UpdateJobRequirementRequest(BaseModel):
    hash: str
    job_requirements: JobRequirementSchema
    recruiter_info: RecruiterInfo
    status: Optional[int]

class UpdateRecruiterInfo(BaseModel):
    hash: str
    recruiter_info: RecruiterInfo

class FunctionCall(BaseModel):
    name: str
    arguments: str  # Keep as a string because it's JSON encoded

class ToolCall(BaseModel):
    id: str
    type: str
    function: FunctionCall

class ToolCallMessage(BaseModel):
    role: str
    content: Optional[str] = None  
    tool_calls: List[ToolCall] = Field(default_factory=list)

class ConversationHistory(BaseModel):
    role: str
    content: Optional[str] = None  

class Transcript(BaseModel):
    transcript: List[ConversationHistory]


class ConversationRequest(BaseModel):
    transcript: List[ConversationHistory]
    tool_calls: List[ToolCallMessage]



class RecruiterRequisitionCreateSchema(BaseModel):
    title: str
    description : Optional[str] = Field(description="Job description.")
    salary_range : Optional[str] = Field(description="Salary range.")
    location_type : Optional[str] = Field(description="Location type (e.g., On-site, Remote, Hybrid).")
    job_type : Optional[str] = Field(description="Job type (e.g., Full-time, Part-time, Contract).")
    level: Optional[str] = Field(description="Requisition Level")
    long_description: Optional[str] = Field(description="Full job description in html format")
    show_salary: Optional[bool] = Field(description="Whether to show the salary range.")
    salary_details: Optional[str] = Field(description="Salary details for the position.")
    llm_requistion_details: Optional[List[str]]= Field(description="List of major requirements")
    binary_requirements: Optional[List[Optional[str]]]= Field(description="List of binary requirements")


class RequisitionProfession(BaseModel):
    name: str