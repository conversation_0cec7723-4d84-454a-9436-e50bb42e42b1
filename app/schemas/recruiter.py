from pydantic import BaseModel, EmailStr, validator, root_validator, validator
from email_validator import validate_email, EmailNotValidError
from typing import Optional, ClassVar, List
from datetime import date, datetime
from enum import Enum
import re
from fastapi import Form


class Recruiter(BaseModel):
    id: int
    first_name: str
    last_name: str
    email: str
    phone: Optional[str] = None
    status: Optional[int] = 1
    organization_id: Optional[int] 
    created_at: Optional[datetime] = None

class RecruiterBaseMandatory(BaseModel):
    first_name: str
    last_name: Optional[str]=None
    email: str

class RecruiterCreateByVapi(BaseModel):
    first_name: str
    last_name: Optional[str]
    email: str
    phone: Optional[str]
    password: str


class RecruiterResponse(Recruiter):
    access_token: Optional[str] = None


class RecruiterCreate(RecruiterBaseMandatory):
    phone: Optional[str] = None
    password: str

    # Constants
    MIN_NAME_LENGTH: ClassVar[int] = 2
    MIN_PASSWORD_LENGTH: ClassVar[int] = 8

    @validator("first_name")
    def validate_first_name(cls, value):
        if not value.strip():
            raise ValueError("First name is required")
        if len(value) < cls.MIN_NAME_LENGTH:
            raise ValueError(
                f"First name must be at least {cls.MIN_NAME_LENGTH} characters long"
            )
        return value

    @validator("last_name")
    def validate_last_name(cls, value):
        if not value.strip():
            raise ValueError("Last name is required")
        if len(value) < cls.MIN_NAME_LENGTH:
            raise ValueError(
                f"Last name must be at least {cls.MIN_NAME_LENGTH} characters long"
            )
        return value

    @validator("email")
    def validate_email(cls, value):
        if not value.strip():
            raise ValueError("Email is required")
        try:
            validate_email(value)
        except EmailNotValidError:
            raise ValueError("Invalid Email Address")
        return value

    @validator("password")
    def validate_password(cls, value):
        if not value.strip():
            raise ValueError("Password is required")
        if len(value) < cls.MIN_PASSWORD_LENGTH:
            raise ValueError(
                f"Password must be at least {cls.MIN_PASSWORD_LENGTH} characters long"
            )
        return value

    # @validator("confirm_password")
    # def validate_confirm_password(cls, value, values):
    #     password = values.get("password")
    #     if not value.strip():
    #         raise ValueError("Confirm password is required")
    #     if password and value != password:
    #         raise ValueError("Passwords do not match")
    #     return value

    # class Config:
    #     extra = 'forbid'
