from celery_config import celery_app
from database import sessionLocal
from sqlalchemy.orm import Session
import logging
from services.requisition.recommendation import CandidateRecommendation
from models.requisition import Requisition
from models.professions import Professions
import json
from services.parser.requisition_parser import RequisitionParserServiceClass
from services.requisition.requisition_similarity import RequisitionSimilarity
from services.external.opensearch import OpenSearchServiceClass
from dependencies import OPENSEARCH_RECRUITER_REQUISITION_INDEX
from helper.html_stripper import strip_html_tags
from services.requisition.requisition_requirements import RequisitionRequirementsServiceClass
logger = logging.getLogger(__name__)

@celery_app.task(name="celery_tasks.post_requisition_process_tasks.create_requisition_vector_embeddings")
def create_requisition_vector_embeddings(requisition_id: int):
    logger.info(
        f" ===================== Executing create_requisition_vector_embeddings task for requisition ID: {requisition_id}"
    )

    db = sessionLocal()

    try:
        response = RequisitionSimilarity().store_embeddings(requisition_id, db)

        logger.info(f" ===================== Completed create_requisition_vector_embeddings task for requisition ID: {requisition_id}")
    except Exception as e:
        logger.error(
            f"Error creating recommendations for requisition ID: {requisition_id}: {e}"
        )
    finally:
        db.close()

@celery_app.task(name="celery_tasks.post_requisition_process_tasks.create_recommended_candidates_from_similar_requisitions")
def create_recommended_candidates_from_similar_requisitions(requisition_id: int):
    logger.info(
        f" ===================== Executing create_recommended_candidates_from_similar_requisitions task for requisition ID: {requisition_id}"
    )

    db = sessionLocal()

    try:
        response = CandidateRecommendation().create_recommended_candidates_from_similar_requisitions(
            requisition_id, db
        )

        logger.info(f" ===================== create_recommended_candidates_from_similar_requisitions task completed for requisition ID: {requisition_id}")
    except Exception as e:
        logger.error(
            f"Error creating recommendations for requisition ID: {requisition_id}: {e}"
        )
    finally:
        db.close()


@celery_app.task(name="celery_tasks.post_requisition_process_tasks.create_evaluation_criteria")
def create_evaluation_criteria(requisition_id: int):
    """
    Celery task to create evaluation criteria and recommendations for a given requisition.

    Args:
        requisition_id (int): The ID of the requisition.

    Raises:
        Exception: If an error occurs during the recommendation process.
    """

    logger.info(
        f"Executing create_evaluation_criteria task for requisition ID: {requisition_id}")

    db = sessionLocal()

    try:
        response = CandidateRecommendation().create_evaluation_criteria(
            requisition_id, db)

        return response
    except Exception as e:
        logger.error(
            f"Error creating recommendations for requisition ID: {requisition_id}: {e}")
        raise e
    finally:
        db.close()


@celery_app.task(name="celery_tasks.post_requisition_process_tasks.llm_requisition_detail")
def add_llm_requisition_detail(
    requisition_id,
    long_description,
):
    logger.info(f"==================== Add llm requisition process started ================")
    db: Session = sessionLocal()
    try:
        logger.info(f"Requisition ID:{requisition_id}")
        requisition = (
            db.query(Requisition).filter(Requisition.id == requisition_id).first()
        )
        requisition_industry = (
            db.query(Professions)
            .filter(Professions.id == requisition.profession_id)
            .first()
        )
        clean_description=strip_html_tags(long_description)
        parsed_req_cleaned=RequisitionParserServiceClass().process_requsitions(clean_description)
        job_details = parsed_req_cleaned.get("job_details", {})
        company_details = parsed_req_cleaned.get("company_details", {})
        requirements=job_details.get("requirements_and_responsibilities",[])
        industry=company_details.get("industry",[])
        llm_req = RequisitionParserServiceClass().parse_cleaned_requriements(
            requirements,industry
        )
        logger.info(f"clean_req:{llm_req}")
        technicl_req = llm_req.get("technical_requirements")
        binary_req = llm_req.get("binary_requirements")
        requisition.llm_requistion_details = (json.dumps(technicl_req) if technicl_req else None,)
        requisition.binary_requirements = (json.dumps(binary_req) if binary_req else None,)
        db.commit()
        db.refresh(requisition)
        logger.info(f"================= Add llm requisition process completed ====================== ")
    except Exception as e:
        logger.error(f"Add llm requisition process failed: {e}")
    finally:
        db.close()



@celery_app.task(name="celery_tasks.post_requisition_process_tasks.insert_requisition_details_to_opensearch")
def insert_requisition_details_to_opensearch(document_id,data):
    logger.info(f"==================== Inserting recruiter requisition details into OpenSearch ================")

    try:
        opensearch_service_object = OpenSearchServiceClass(OPENSEARCH_RECRUITER_REQUISITION_INDEX)
        response = opensearch_service_object.insert_into_opensearch(
            document_id=document_id, data=data
        )
        logger.info(f"Inserting recruiter requisition details into OpenSearch completed")
        return True
    except Exception as e:
        logger.error(
            f"Error inserting recruiter requisition details into OpenSearch: {e}"
        )
        raise e

@celery_app.task(name="celery_tasks.post_requisition_process_tasks.get_requisition_requirements")
def get_requisition_requirements(req_id):
    try:
        logger.info(f"==================== Getting requisition requirements started================")
        db = sessionLocal()
        requisition_requirements_service = RequisitionRequirementsServiceClass()
        parsed_requirements = requisition_requirements_service.get_requirements(req_id, db)
        logger.info(f"Requisition requirements fetched successfully for req_id: {req_id}")
        return True
        
    except Exception as e:
        logger.error(
            f"Error getting requisition requirements for req_id {req_id}: {e}"
        )
        return False
    finally:
        db.close()

