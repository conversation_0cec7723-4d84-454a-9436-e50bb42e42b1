from celery_config import celery_app
from database import sessionLocal
from sqlalchemy.orm import Session
import logging
from fastapi import (
    APIRouter,
    status,
    HTTPException,
)
from sqlalchemy.orm import Session
from schemas.response import ApiSuccessResponse
from custom_exceptions import *
from services.qc_automation.qc_automation import QCAutomationClass
from models.qc_automation import UserProfileRevisions
qcautomation_service = QCAutomationClass()


router = APIRouter()

logger = logging.getLogger(__name__)
@celery_app.task(name="qc-automation", queue="interview_invite_queue")
def qc_automation(user_id):
    try:
        db: Session = sessionLocal()
        response = qcautomation_service.get_user_data(user_id, db)
        
        # Ensure response is a dict or JSON-serializable
        if not isinstance(response, dict):
            raise ValueError("Response from get_user_data must be a dictionary")

        # Check if the user_id already exists in the database
        existing_record = db.query(UserProfileRevisions).filter_by(user_id=user_id).first()

        if existing_record:
            # Update the existing record
            existing_record.data = response
            db.commit()
            db.refresh(existing_record)
        else:
            # Create a new record
            qc_record = UserProfileRevisions(
                user_id=user_id,
                data=response  # response is ensured to be a dict
            )
            db.add(qc_record)
            db.commit()
            db.refresh(qc_record)
        
        return True
    except Exception as e:
        logger.error(f"Error in qc_automation task: {e}")
        return False