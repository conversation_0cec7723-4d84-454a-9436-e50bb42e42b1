from celery_tasks.enrich_company_info import enrich_company_info
from services.interview.interview import InterviewServiceClass
from services.interview.candidate_picture import VideoProcessor
from celery_config import celery_app
from models.user import User
from database import sessionLocal
from sqlalchemy.orm import Session
import logging
from fastapi import Request
from services.user.resume_builder import ResumeBuilderServiceClass
import time
from services.media.video_processing import VideoProcessingServiceClass
from models.user_requisition import UserRequisition
from services.interview.interview import InterviewServiceClass
from dependencies import OPENSEARCH_RC_INDEX
from services.external.opensearch import OpenSearchServiceClass

logger = logging.getLogger(__name__)

@celery_app.task(name="interview-post-process", queue="interview_post_process_queue")
def interview_post_process(user_id, interview_id):
    interview_service_object = InterviewServiceClass()
    db: Session = sessionLocal()
    mock_scope = {
        "type": "http",
        "method": "GET",
        "headers": [],
    }
    request = Request(scope=mock_scope)
    try:
        user = db.query(User).filter(User.id == user_id).first()
        interview = interview_service_object.get_interview_by_id(interview_id, db)
        request.state.user = user

        logger.info("Interview post process task celery init")
        logger.info(f"Interview id: {interview.id} user id: {user.id}")
        interview_feedback = interview_service_object.generate_interview_feedback(interview, db)

        logger.info(f"Interview feedback generated with id: {interview_feedback.id}")

        if user.profile_pic is None:
            VideoProcessor().get_interview_videos_with_thumbnails(interview.id, user.id, request, db)

        interview_service_object.summarize_interview_questions(interview.id, db)

        interview_transcript = interview_service_object.get_interview_transcript(
            interview_id, db
        )
        ResumeBuilderServiceClass().extract_user_profile(interview, interview_transcript, request, db)

        user_requisition = (
            db.query(UserRequisition)
            .filter(
                UserRequisition.user_id == user.id,
                UserRequisition.requisition_id == interview_feedback.requisition_id,
                UserRequisition.is_applied == 1,
            )
            .first()
        )
        logger.info(f"""User requisition found with version: {user_requisition.interview_version}""")
        if user_requisition.interview_version != "v2":
            VideoProcessingServiceClass().map_video_link_to_question(interview_feedback,db)
        else:
            VideoProcessingServiceClass().sync_video_link_with_s3(
                interview_feedback, db
            )

        #Insert candidate into opensearch
        exists = OpenSearchServiceClass(index_name=OPENSEARCH_RC_INDEX).check_id_exists(interview_feedback.user_id)
        
        if exists:
            logger.info(f"User id already exists in opensearch: {interview_feedback.user_id}")
            interview_service_object.update_user_opensearch(interview_feedback.user_id, db)
        else:
            converted_data = interview_service_object.convert_data(interview_feedback.user_id, db)
            if converted_data:
                inserted_record = interview_service_object.insert_record_to_opensearch(converted_data, db)
                logger.info(f"User inserted into OpenSearch: {inserted_record}")
            
        enrich_company_info.delay(interview_feedback.user_id)        
        # CANDIDATE INTRO VIDEO WORK
        # background_tasks.add_task(
        #     IntroVideoServiceClass().get_candidate_intro,interview.id,request,db
        # )

        ## Uncomment this when plagiarism check is implemented
        # background_tasks.add_task(
        #     self.check_interview_plagiarism, update_interview.id, db
        # )
    finally:
        db.close()
