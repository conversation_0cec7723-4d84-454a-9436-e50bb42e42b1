
from celery_config import celery_app
import logging
from database import sessionLocal
from sqlalchemy.orm import Session
from services.scrapper.scrapping_dog import ScrappingDogServiceClass
from models.user import User
from models.interview_feedback import InterviewFeedback
from services.user.user import UserServiceClass
from services.external.opensearch import OpenSearchServiceClass
from services.external.openai import OpenAIServiceClass
from dependencies import OPENSEARCH_RC_INDEX
from sqlalchemy import and_
scrapingdog_service_object = ScrappingDogServiceClass()
openai_service = OpenAIServiceClass()
user_service_object = UserServiceClass()

logger = logging.getLogger(__name__)

@celery_app.task(name="enrich-company-info", queue="enrichment_and_insertion", routing_key="enrichment_and_insertion")
def enrich_company_info(user_id, scrape_all_data=False):
    db :Session = sessionLocal()
    opensearch_service = OpenSearchServiceClass(index_name=OPENSEARCH_RC_INDEX)

    try:
        enriched_in_db=None
        if scrape_all_data:
            enriched_in_db = scrapingdog_service_object.store_user_data_via_linkedin_scrapingdog(user_id, db)
        else:
            enriched_in_db = scrapingdog_service_object.get_linkedin_profile_by_user_id(
                user_id, db
            )
        
        if enriched_in_db:
            exists = opensearch_service.check_id_exists(user_id)
            if exists: # ab tm check karoge enrichment key exist krti ya nai: agar keti, to no update and continue, wrna upadate krdena...
                # update the company info
                logger.info("User already exists in opensearch, updating info")

                enriched = opensearch_service.check_enriched_status(user_id=user_id)
                if not enriched:

                    user_experience_list = user_service_object.get_user_experience_list(user_id, db)

                    work_experience_text = "\n".join([
                                f"Job title: {exp['job_title']}, working at {exp['organization']}. "
                                f"Start date is {exp['from_date']} and end date is {exp['to_date']}\n"
                                f"Employment type: {exp['emp_type']}, Location: {exp['location_type']}\n"
                                f"User experience description: {exp['description']}\n"
                                f"Company information: {exp['about_company']}"
                                for exp in user_experience_list
                            ])
                    logger.info("Updating embeddings for existing user in opensearch...")
                    work_experience_embeddings_result = OpenAIServiceClass(
                    ).get_embeddings(text=work_experience_text.lower())
                    work_experience_embeddings = work_experience_embeddings_result[
                        'vector'] if work_experience_embeddings_result else None

                    # Perform partial update to OpenSearch
                    update_payload = {
                        "doc": {
                            "embeddings": {
                                "work_experience": work_experience_embeddings
                            }
                        }
                    }

                    opensearch_service.update_work_experience_in_opensearch(
                        person_id=user_id, work_experience_payload=user_experience_list)

                    opensearch_service.update_work_experience_embedding_by_user_id(user_id, update_payload)
                    
                    opensearch_service.set_enriched_status_true(user_id=user_id)
                    
                    logger.info("Updated user company information and work experience embeddings")
            
            else:
                logger.info("New user in opensearch, inserting...")
                user_service_object.insert_single_user_to_opensearch(user_id, db)
                opensearch_service.set_enriched_status_true(user_id=user_id)
        
        return True

    except Exception as e:
        logger.error("Error in scraping user profile")
        logger.error(str(e))

@celery_app.task(name="enrich-company-info-bulk", queue="bulk_queue")
def enrich_company_info_bulk_all_users(user_id: int):
    db: Session = sessionLocal()
    try:
        enrich_company_info(user_id)
    except Exception as e:
        logger.error(f"Error enriching LinkedIn profile for user_id={user_id}: {e}")
    finally:
        db.close()

@celery_app.task(name="enrich-company-info-bulk-service", queue="bulk_queue")
def enrich_company_info_bulk_all_users_service():
    db: Session = sessionLocal()
    try:
        user_ids = (
            db.query(User.id)
            .join(InterviewFeedback, InterviewFeedback.user_id == User.id)
            .filter(
                InterviewFeedback.score >= 60,
                User.linkedin_url != ''
            )
            .distinct()
            .all()
        )

        for (user_id,) in user_ids:
            enrich_company_info_bulk_all_users.delay(user_id)

    except Exception as e:
        logger.error("Error in enrich_company_info_bulk_all_users_service")
        logger.exception(e)
    finally:
        db.close()


@celery_app.task(name="enrich-company-info-bulk-linkedinID", queue="bulk_queue")
def enrich_company_info_bulk_linkedinID(user_id: int):
    db: Session = sessionLocal()
    try:
        enrich_company_info(user_id, scrape_all_data=True)
    except Exception as e:
        logger.error(f"Error enriching LinkedIn profile for user_id={user_id}: {e}")
    finally:
        db.close()


@celery_app.task(name="enrich-company-info-bulk-linkedinID-service", queue="bulk_queue")
def enrich_company_info_bulk_linkedinID_service():
    db: Session = sessionLocal()
    try:
        user_ids = db.query(User.id).filter(
            and_(
                User.linkedin_url.isnot(None),
                User.resume_link.is_(None)
            )
        ).all()

        for (user_id,) in user_ids:
            enrich_company_info_bulk_linkedinID.delay(user_id)

    except Exception as e:
        logger.error("Error in enrich_company_info_bulk_linkedinID_service")
        logger.exception(e)
    finally:
        db.close()
