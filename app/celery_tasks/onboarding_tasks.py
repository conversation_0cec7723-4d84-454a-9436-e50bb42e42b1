from services.external.bedrock import BedrockService
from celery_config import celery_app
from database import sessionLocal, get_mongodb_client
from sqlalchemy.orm import Session
import logging
from services.onboarding.job_onboarding import JobOnboardingServiceClass
from fastapi import BackgroundTasks, Request
from services.recruiter.candidate_evaluation import (
    CandidateEvaluationServiceClass,
)
import json
from helper.prompt import get_cold_email_prompt, get_cold_email_prompt_without_candidates
from helper.parser import parse_email_body_to_html, parse_email_body_to_html_without_candidates
from services.external.openai import OpenAIServiceClass
from schemas.email import ColdEmailSchema, ColdEmailSchemaWithoutCandidates
from services.requisition.requisition import RequisitionServiceClass
from services.notification.email import send_email_background
from models.recruiter import Recruiter
from services.auth import AuthServiceClass
from services.external.llama import LlamaServiceClass
from services.external.openai import OpenAIServiceClass
from services.recruiter.outreach import RecruiterOutreachServiceClass

logger = logging.getLogger(__name__)

recruiter_outreach_service = RecruiterOutreachServiceClass()


@celery_app.task(name="job-onboarding-from-mongo", queue="onboarding_queue")
def job_onboarding_from_mongo():
    db: Session = sessionLocal()
    mongo_client: Session = get_mongodb_client()
    background_tasks = BackgroundTasks()
    # Mock scope for the Request object
    mock_scope = {
        "type": "http",
        "method": "GET",
        "headers": [],
    }
    request = Request(scope=mock_scope)
    try:
        logger.info("job onboarding from mongo task celery init")
        job_onboarding_service_object = JobOnboardingServiceClass()
        response = job_onboarding_service_object.on_board_jobs_to_system(
            request, background_tasks, db, mongo_client
        )
        logger.info(str(response))
        # job_onboarding_service_object.on_board_jobs_to_system(request, background_tasks, db, mongo_client)
    finally:
        db.close()


@celery_app.task(name="send-cold-email-to-recruiter", queue="onboarding_queue")
def send_cold_email_to_recruiter(requisition_id, hash=None):
    db: Session = sessionLocal()
    background_tasks = BackgroundTasks()
    # Mock scope for the Request object
    mock_scope = {
        "type": "http",
        "method": "GET",
        "headers": [],
    }
    request = Request(scope=mock_scope)
    request._query_params = {"per_page": "5"}
    try:

        logger.info(f"Sending cold email for requisition_id: {requisition_id}")
        requisition = RequisitionServiceClass().get_requisition_by_id(
            requisition_id, db
        )
        recruiter = (
            db.query(Recruiter)
            .filter(Recruiter.organization_id == requisition.organization_id)
            .first()
        )
        if recruiter is not None:
            logger.info(f"Recruiter found with id: {recruiter.id}")
            recruiter.access_token = AuthServiceClass().create_access_token(recruiter.email)
            candidates = CandidateEvaluationServiceClass().get_recommended_candidates(
                requisition_id, request, db
            )
            logger.info(f"Candidates count: {candidates['pagination']['per_page']}")
            # logger.info(f"Candidates: {candidates['items']}")
            parsed_response=None

            if len(candidates['items']) > 0:
                prompt = get_cold_email_prompt(
                    requisition, candidates["items"], recruiter
                )
                logger.info(f"Prompt generated: {prompt}")
                messages = [{"role": "user", "content": prompt}]

                response = OpenAIServiceClass().get_cold_email(
                    messages,
                    ColdEmailSchema,
                    "Writing a cold email to the hiring manager",
                )
                parsed_response = parse_email_body_to_html(response["body"], requisition.title)

            else:
                prompt = get_cold_email_prompt_without_candidates(
                    requisition, recruiter
                )
                logger.info(f"Prompt generated: {prompt}")

                msg=[{'role': 'user', 'content': [{'text': prompt}]}]
                response = BedrockService().llm_json(messages=msg, parameters=ColdEmailSchemaWithoutCandidates, function_description="Writing a cold email to the hiring manager")
                parsed_response = parse_email_body_to_html_without_candidates(response["body"])

            email_body = {"data": parsed_response}
            req_data = {
                "requisition_id": requisition_id,
                "receiver_email": recruiter.email,
                "ai_subject": response["subject"],
                "ai_content": parsed_response,
                "hash": hash,
            }
            recruiter_outreach_service.create_recruiter_outreach_activity(req_data, db)
        else:
            logger.info(f"No recruiter found for the requisition: {requisition_id}")
    finally:
        db.close()


@celery_app.task(name="send-recruiter-outreach-email", queue="onboarding_queue")
def send_recruiter_outreach_email(outreach_id):
    db: Session = sessionLocal()
    try:
        logger.info(f"send email from route task init")
        logger.info(f"outreach_id: {outreach_id}")
        recruiter_outreach_service.send_recruiter_outreach_email(outreach_id, db)
    finally:
        db.close()
