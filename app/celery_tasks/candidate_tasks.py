from celery_config import celery_app
from database import sessionLocal
from sqlalchemy.orm import Session
import logging
from services.requisition.recommendation import CandidateRecommendation
from models.requisition import Requisition
from models.professions import Professions
import json
from services.parser.requisition_parser import RequisitionParserServiceClass
from services.requisition.requisition_similarity import RequisitionSimilarity
from services.external.opensearch import OpenSearchServiceClass
from dependencies import OPENSEARCH_RECRUITER_REQUISITION_INDEX
from helper.html_stripper import strip_html_tags
from services.requisition.requisition_requirements import RequisitionRequirementsServiceClass
logger = logging.getLogger(__name__)



@celery_app.task(name="evaluate_candidate", queue="enrichment_and_insertion", routing_key="enrichment_and_insertion")
def evaluate_candidate(user_id,req_id):
    try:
        logger.info(f"====================Evaluate Canididate task started================")
        db = sessionLocal()
        requisition_requirements_service = RequisitionRequirementsServiceClass()
        requisition_requirements_service.get_requirement_evaluation(user_id,req_id, db)
        logger.info(f"Requisition requirements fetched successfully for req_id: {req_id}")
        return True
        
    except Exception as e:
        logger.error(
            f"Error evaluating candidate with user_id {user_id}: {e}"
        )
        return False
    finally:
        db.close()