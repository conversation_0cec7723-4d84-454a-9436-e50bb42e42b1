from celery_config import celery_app
from database import sessionLocal
from sqlalchemy.orm import Session
import logging
from fastapi import BackgroundTasks, Request
from services.bulk_service import BulkEducationService
from services.interview.interview import InterviewServiceClass
interview_service = InterviewServiceClass()
logger = logging.getLogger(__name__)


@celery_app.task(name="data-migration-opensearch")
def migrate_data():
    db: Session = sessionLocal()
    try:
        logger.info(f"Starting data migration from postgres to opensearch")
        response = interview_service.bulk_insert_to_opensearch(db)
    finally:
        db.close()


@celery_app.task(name="data-migration-updation-opensearch")
def migrate_data_update():
    db: Session = sessionLocal()
    try:
        logger.info(f"Starting data updation from postgres to opensearch")
        response = interview_service.bulk_update_to_opensearch(db)
    finally:
        db.close()


@celery_app.task(name="data-migration-opensearch-users-table")
def migrate_data_users_table():
    db: Session = sessionLocal()
    try:
        logger.info(
            f"Starting data migration from postgres to opensearch - USERS TABLE")
        response = interview_service.bulk_insert_to_opensearch_users_table(db)
    finally:
        db.close()


@celery_app.task(name="insert-users-to-opensearch", queue="interview_invite_queue")
def insert_users_to_opensearch():
    db: Session = sessionLocal()
    try:
        logger.info(f"Starting user insertion background task")

        response = interview_service.insert_users_to_opensearch(db)

        return response
    finally:
        db.close()


@celery_app.task(name="education-update-opensearch", queue="bulk_queue")
def education_update():
    """
    Master task that gets all user IDs and creates batches for worker tasks.
    """
    try:
        logger.info(f"Starting education embedding update - Master task")

        # Get all user IDs using bulk service
        bulk_service = BulkEducationService()
        all_user_ids = bulk_service.get_all_user_ids()

        if not all_user_ids:
            logger.warning("No user IDs found in OpenSearch")
            return {"message": "No users found", "batches_created": 0}

        logger.info(f"Found {len(all_user_ids)} users in OpenSearch")

        # Create batches of 100 users each
        batch_size = 100
        batches = []

        for i in range(0, len(all_user_ids), batch_size):
            batch = all_user_ids[i:i + batch_size]
            batches.append(batch)

        logger.info(f"Created {len(batches)} batches of {batch_size} users each")

        # Launch worker tasks for each batch
        for batch_index, batch in enumerate(batches):
            logger.info(f"Launching worker task for batch {batch_index + 1}/{len(batches)}")
            education_update_batch.delay(batch, batch_index + 1)

        return {
            "message": "Education embedding update started successfully",
            "total_users": len(all_user_ids),
            "batches_created": len(batches),
            "batch_size": batch_size
        }

    except Exception as e:
        logger.error(f"Error in education update master task: {e}")
        return {"error": str(e)}


@celery_app.task(name="education-update-batch", queue="bulk_queue")
def education_update_batch(user_ids_batch, batch_number):
    """
    Worker task that processes a batch of user IDs for education embeddings.
    """
    try:
        logger.info(f"Starting education embedding update for batch {batch_number} with {len(user_ids_batch)} users")

        # Use bulk service for processing
        bulk_service = BulkEducationService()
        result = bulk_service.process_education_embeddings_batch(user_ids_batch)

        logger.info(f"Completed batch {batch_number}: {result}")
        return result

    except Exception as e:
        logger.error(f"Error in education update batch {batch_number}: {e}")
        return {"error": str(e)}
