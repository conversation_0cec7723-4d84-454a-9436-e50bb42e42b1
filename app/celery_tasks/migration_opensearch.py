from celery_config import celery_app
from database import sessionLocal
from sqlalchemy.orm import Session
import logging
from fastapi import BackgroundTasks, Request
from services.external.opensearch import OpenSearchServiceClass
from dependencies import OPENSEARCH_RC_INDEX
from services.interview.interview import InterviewServiceClass
interview_service = InterviewServiceClass()
logger = logging.getLogger(__name__)


@celery_app.task(name="data-migration-opensearch")
def migrate_data():
    db: Session = sessionLocal()
    try:
        logger.info(f"Starting data migration from postgres to opensearch")
        response = interview_service.bulk_insert_to_opensearch(db)
    finally:
        db.close()


@celery_app.task(name="data-migration-updation-opensearch")
def migrate_data_update():
    db: Session = sessionLocal()
    try:
        logger.info(f"Starting data updation from postgres to opensearch")
        response = interview_service.bulk_update_to_opensearch(db)
    finally:
        db.close()


@celery_app.task(name="data-migration-opensearch-users-table")
def migrate_data_users_table():
    db: Session = sessionLocal()
    try:
        logger.info(
            f"Starting data migration from postgres to opensearch - USERS TABLE")
        response = interview_service.bulk_insert_to_opensearch_users_table(db)
    finally:
        db.close()


@celery_app.task(name="insert-users-to-opensearch", queue="interview_invite_queue")
def insert_users_to_opensearch():
    db: Session = sessionLocal()
    try:
        logger.info(f"Starting user insertion background task")

        response = interview_service.insert_users_to_opensearch(db)

        return response
    finally:
        db.close()
