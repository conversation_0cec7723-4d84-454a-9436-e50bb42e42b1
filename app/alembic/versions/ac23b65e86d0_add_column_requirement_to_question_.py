"""add column requirement to question_feedback

Revision ID: ac23b65e86d0
Revises: 1601d0134ae1
Create Date: 2025-04-16 16:12:41.137178

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ac23b65e86d0'
down_revision: Union[str, None] = '1601d0134ae1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column(
        "question_feedbacks",
        sa.Column("requirement", sa.Text, nullable=True),
    )


def downgrade() -> None:
    op.drop_column('question_feedbacks', 'requirement')
