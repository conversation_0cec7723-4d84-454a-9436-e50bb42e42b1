"""add column opensearch_status to users

Revision ID: 95200c5f02ad
Revises: 8de7d3ee5971
Create Date: 2025-04-17 15:47:15.479973

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '95200c5f02ad'
down_revision: Union[str, None] = '8de7d3ee5971'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column(
        "users",
        sa.Column("opensearch_status", sa.Integer, default=0),
    )


def downgrade() -> None:
    op.drop_column("users", "opensearch_status")
