"""create qc_automation table

Revision ID: 54b256a686cf
Revises: 95200c5f02ad
Create Date: 2025-05-15 07:10:21.424714

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '54b256a686cf'
down_revision: Union[str, None] = '95200c5f02ad'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        "user_profile_revisions",
        sa.Column("id", sa.Integer, primary_key=True),
        sa.Column("user_id", sa.Integer, sa.<PERSON>ey("users.id"), nullable=False),
        sa.Column("data", sa.JSON, nullable=True),
        sa.Column(
            "created_at", sa.TIMESTAMP, server_default=sa.func.current_timestamp()
        ),
        sa.Column(
            "updated_at", sa.TIMESTAMP, server_default=sa.func.current_timestamp(),
            onupdate=sa.func.current_timestamp()
        ),
    )


def downgrade() -> None:
    op.drop_table("user_profile_revisions")