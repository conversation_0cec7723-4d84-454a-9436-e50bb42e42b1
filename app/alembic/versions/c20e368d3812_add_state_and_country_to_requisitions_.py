"""add_state_and_country_to_requisitions_table

Revision ID: c20e368d3812
Revises: 4f8bbd7fb7e1
Create Date: 2025-06-02 07:59:21.716757

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c20e368d3812'
down_revision: Union[str, None] = '4f8bbd7fb7e1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("requisitions", sa.Column("country_id", sa.Integer, nullable=True))
    op.add_column("requisitions", sa.Column("state_id", sa.Integer, nullable=True))


def downgrade() -> None:
    op.drop_column("requisitions", "country_id")
    op.drop_column("requisitions", "state_id")
