"""create_user_evaluations_table

Revision ID: 7edcf5e9e538
Revises: 50ae40f280a4
Create Date: 2025-06-18 12:39:10.728871

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7edcf5e9e538'
down_revision: Union[str, None] = '50ae40f280a4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        "user_evaluations",
        sa.Column("id", sa.BigInteger, primary_key=True),
        sa.Column("requisition_id", sa.Integer, nullable=False),
        sa.Column("user_id", sa.Integer, nullable=False, index=True),
        sa.Column("feedback", sa.Text, nullable=False),
        sa.Column("status", sa.Integer, nullable=False),
        sa.Column(
                "created_at", sa.TIMESTAMP, server_default=sa.func.current_timestamp()
            ),
        sa.Column(
                "updated_at", sa.TIMESTAMP, server_default=sa.func.current_timestamp(),
                onupdate=sa.func.current_timestamp()
            ),
        )


def downgrade() -> None:
    op.drop_table("user_evaluations")
