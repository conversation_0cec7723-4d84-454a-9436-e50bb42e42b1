"""add_state_id_and_timezone_to_cities

Revision ID: fd39d3e3a445
Revises: 222fe934b10e
Create Date: 2025-04-21 11:03:12.916515

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fd39d3e3a445'
down_revision: Union[str, None] = '222fe934b10e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column(
        "cities",
        sa.Column("state_id", sa.BIGINT,nullable=True,default=0,index=True),
    )
    op.add_column(
        "cities",
        sa.Column("timezone", sa.VARCHAR,nullable=True),
    )


def downgrade() -> None:
    op.drop_column('cities', 'state_id')
    op.drop_column('cities', 'timezone')
