"""add_parsed_requirements_column_in_requisitions

Revision ID: 50ae40f280a4
Revises: c20e368d3812
Create Date: 2025-06-18 07:39:23.234184

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '50ae40f280a4'
down_revision: Union[str, None] = 'c20e368d3812'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column(
        "requisitions",
        sa.Column("parsed_requirements", sa.Text, nullable=True, default=None),
    )


def downgrade() -> None:
    op.drop_column("requisitions", "parsed_requirements")
