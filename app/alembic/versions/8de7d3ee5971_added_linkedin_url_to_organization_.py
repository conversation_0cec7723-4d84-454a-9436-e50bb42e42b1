"""added linkedin url to organization details table

Revision ID: 8de7d3ee5971
Revises: ac23b65e86d0
Create Date: 2025-04-17 11:55:07.417858

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8de7d3ee5971'
down_revision: Union[str, None] = 'ac23b65e86d0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('organization_details', sa.Column('linkedin_url', sa.String(), nullable=True))


def downgrade() -> None:
    op.drop_column('organization_details', 'linkedin_url')
