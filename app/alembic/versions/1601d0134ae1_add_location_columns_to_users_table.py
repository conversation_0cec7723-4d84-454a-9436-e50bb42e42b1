"""add_location_columns_to_users_table

Revision ID: 1601d0134ae1
Revises: fd39d3e3a445
Create Date: 2025-04-23 12:17:23.639782

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "1601d0134ae1"
down_revision: Union[str, None] = "fd39d3e3a445"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column("users", sa.Column("country_id", sa.Integer, nullable=True))
    op.add_column("users", sa.Column("location", sa.Text, nullable=True))
    op.add_column("users", sa.Column("state_id", sa.Integer, nullable=True))


def downgrade() -> None:
    op.drop_column("users", "country_id")
    op.drop_column("users", "location")
    op.drop_column("users", "state_id")
