"""add linkedinId column in organizations table

Revision ID: 4f8bbd7fb7e1
Revises: f52747ca0a69
Create Date: 2025-05-19 08:37:44.559393

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4f8bbd7fb7e1'
down_revision: Union[str, None] = 'f52747ca0a69'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column(
        "organizations",
        sa.Column("linkedin_id", sa.Text,nullable=True),
    )


def downgrade() -> None:
    op.drop_column("organizations", "linkedin_id")

