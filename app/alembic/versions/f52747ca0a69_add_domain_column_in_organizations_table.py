"""add domain column in organizations table

Revision ID: f52747ca0a69
Revises: 54b256a686cf
Create Date: 2025-05-19 08:35:07.198222

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f52747ca0a69'
down_revision: Union[str, None] = '54b256a686cf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column(
        "organizations",
        sa.Column("domain", sa.Text,nullable=True),
    )


def downgrade() -> None:
    op.drop_column("organizations", "domain")

