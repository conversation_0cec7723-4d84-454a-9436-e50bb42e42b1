

import os
import base64
import fitz
import json
import logging

logger = logging.getLogger(__name__)

def pdf_to_images(pdf_path, output_dir=None, zoom=2):
    """Convert PDF to JPEG images using PyMuPDF and save to specified directory"""

    if output_dir is None:
        output_dir = os.path.join(os.path.dirname(pdf_path), "images")

    os.makedirs(output_dir, exist_ok=True)

    doc = fitz.open(pdf_path)
    image_paths = []

    for i, page in enumerate(doc):
        # Render the page to a pixmap
        pix = page.get_pixmap(matrix=fitz.Matrix(zoom, zoom))  # zoom controls resolution
        image_path = os.path.join(output_dir, f"page_{i+1}.jpg")
        pix.save(image_path)
        image_paths.append(image_path)

    return image_paths


# Function to encode image to base64
def encode_image(image_path):
    """Encode image to base64"""
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")
