from sentence_transformers import SentenceTransformer, util
import logging

logger = logging.getLogger(__name__)

class SemanticMatcher:
    def __init__(self):
        self.model = SentenceTransformer('sentence-transformers/all-mpnet-base-v2')

    def match_best(self, query, candidates):
        """
        Returns the most similar candidate from the list based on semantic similarity.
        
        Args:
            query (str): Input string to match.
            candidates (list): List of strings to search in.

        Returns:
            str: Most similar string from candidates.
        """
        if not candidates:
            return None

        query_emb = self.model.encode(query)
        candidates_emb = self.model.encode(candidates)

        scores = util.dot_score(query_emb, candidates_emb)[0].cpu().tolist()
        logger.info(scores)
        best_match = sorted(zip(candidates, scores), key=lambda x: x[1], reverse=True)[0][0]
        return best_match


    def match_best_with_score(self, query, candidates, return_score=False):
        """
        Returns the most similar candidate from the list based on semantic similarity.

        Args:
            query (str): Input string to match.
            candidates (list): List of strings to search in.
            return_score (bool): Whether to return similarity score with the match.

        Returns:
            str or (str, float): Best match or (best match, score)
        """
        if not candidates:
            return (None, 0.0) if return_score else None

        query_emb = self.model.encode(query)
        candidates_emb = self.model.encode(candidates)

        scores = util.dot_score(query_emb, candidates_emb)[0].cpu().tolist()
        logger.info(f"Similarity scores: {scores}")

        best_match, best_score = sorted(zip(candidates, scores), key=lambda x: x[1], reverse=True)[0]

        return (best_match, best_score) if return_score else best_match
