from dependencies import RECRUITER_APP_HOST
import logging
import json
import re

logger = logging.getLogger(__name__)

def get_cold_email_prompt(requisition, candidates, recruiter):
    # Base URL with access token for recruiter redirection
    base_url = f"{RECRUITER_APP_HOST}/?token={recruiter.access_token}"
    
    
    # Prepare candidate details (limit to top 5)
    candidate_details = []
    for index, candidate in enumerate(candidates):
        logger.info(f"Adding recommended candidate: {candidate['id']} & user id: {candidate['user']['id']} to prompt")
        if index == 3:
            break

        feedback = candidate.get("feedback", {})

        # If feedback is a string, attempt to parse it as JSON.
        if isinstance(feedback, str):
            try:
                feedback = json.loads(feedback)
            except Exception as e:
                logger.error("Failed to parse feedback as JSON", exc_info=True)
                feedback = {}

        if "evaluation" not in feedback:
            continue
        
        overview = feedback.get("evaluation", {}).get("overview", "None")
        overview = re.sub(r"-\s*", "", overview).replace("\n", " ").strip()


        candidate_details.append(
                f'''{{
        "name": "{candidate["user"]["first_name"]} {candidate["user"]["last_name"]}",
        "overview": "{overview}",
        "profile": "{base_url}&redirection=profile&id={candidate["id"]}"
        }}'''
                )

    candidates_str = "\n".join(candidate_details)

    # Create the context with supporting information
    context = f"""
        Utilize these supporting details for writing an email:

        ### ABOUT VETTIO
        Vettio is an AI-driven recruitment platform that manages the entire hiring process from start to finish, free-of-cost. Our domain expert AI conducts real-time video interviews, assesses candidates, and delivers a shortlist enriched with valuable insights, including recorded interviews, skill evaluations, strengths and weaknesses, and a detailed comparison of each candidate against your job requirements. It helps you find top talent by searching its extensive pool of candidates or by posting job ads when necessary. 

        ### JOB INFORMATION
        Title: {requisition.title}
        Description: {requisition.description}
        Recruiter Email: {recruiter.email}
        Recipient: Hiring Manager

        ### RECOMMENDED CANDIDATES LIST
        {candidates_str}

        ### VETTIO BASE URL IS: {base_url}

        Instructions:
        - Start with a friendly opener addressing the Hiring Manager.
        - In the introduction, introduce Vettio, its mission in recruitment, its core strengths, and how it streamlines the hiring process for free, without human intervention. We use our domain expert AI to find top talent for their organization, and how Vettio's expert AI conducts real-time video interviews and evaluates candidates thoroughly. 
        - Present the list of recommended candidates only from the context given, for the job title, make sure each candidate is added from the context. Do not make up candidates by yourself. For their overview, make sure to only mention relevant information related to the {requisition.title} role, do not mention of any binary requirements related to location, or other things etc.
        - In the call to action, mention that there are other candidates in our database that might be a good fit for the {requisition.title} role. Encourage to jump on a call to understand the recruiter's requirements and find the right talent for them. Include the following HTML link in the call to action: <a href='{base_url}'>our platform</a>.
        """

    # Assemble the final prompt with role, objective, and additional context
    prompt = f"""
        ## Role:
        You are a skilled professional in crafting recruitment-focused cold emails that highlight Vettio's platform and its core strengths.

        ## Objective:
        Draft a cold email to {requisition.organization.name} explaining Vettio's core strengths in recruitment. Address the recipient as the Hiring Manager and refer to the organization as {requisition.organization.name}.

        ## Additional Context for Drafting the Email:
        {context}
        """
    return prompt


def get_cold_email_prompt_without_candidates(requisition, recruiter):
    """
    Generates a structured prompt for drafting a recruitment-focused cold email without candidate details.
    
    Args:
        requisition: An object containing job requisition details such as title, description, and key responsibilities.
        recruiter: An object containing recruiter details including access token, email, and organization.
    
    Returns:
        A formatted string prompt with context and instructions to craft a cold email.
    """
    # Construct the base URL using the recruiter's access token
    base_url = f"{RECRUITER_APP_HOST}/?token={recruiter.access_token}"
    
    # Build the context with supporting information
    context = f"""
    Utilize these supporting details to write an email:

    ### ABOUT VETTIO
    I wanted to take a moment to introduce Vettio, an AI-driven recruitment platform that manages the entire hiring process from start to finish, free-of-cost. Our domain expert AI conducts real-time video interviews, assesses candidates, and delivers a shortlist enriched with valuable insights, including recorded interviews, skill evaluations, strengths and weaknesses, and a detailed comparison of each candidate against your job requirements. It helps you find top talent by searching its extensive pool of candidates or by posting job ads when necessary.

    ### JOB INFORMATION
    
    Recruiter Email: {recruiter.email}
    Recipient: Hiring Manager

    ### VETTIO BASE URL IS: {base_url}

    Instructions:
    - Begin with a friendly opener addressing the Hiring Manager.
    - Start with some nice pleasentries like hope you're doing good. Then begin by introducing Vettio, its mission in recruitment, its core strengths, and how it streamlines the hiring process for free. We use our domain expert AI to find top talent for their organization, and how Vettio's expert AI conducts real-time video interviews and evaluates candidates thoroughly. 
    - In the introduction, don't directly jump to Vettio, but include some pleasantries as well
    - Conclude with a professional call to action that leaves a positive impression.
    - Encourage the hiring manager to schedule a meeting for a quick demo of Vettio's innovative recruitment solutions.
    """
    
    # Assemble the final prompt with role, objective, and context sections
    prompt = f"""
    ## Role:
    You are a skilled professional in crafting recruitment-focused cold emails that highlight Vettio's platform and its core strengths.

    ## Objective:
    Draft a cold email to {requisition.organization.name} explaining Vettio's core strengths in recruitment. Address the recipient as the Hiring Manager and refer to the organization as {requisition.organization.name}.

    ## Additional Context for Drafting the Email:
    {context}
    """
    
    return prompt

# ## Requirements:
# 1. **Subject Line**: Create an attention-grabbing, professional subject line tailored to recruitment.

# 2. **Email Structure**:
#    - **Engaging Introduction**:
#      - Detail highlights of Vettio's mission in recruitment.
#      - Explain all Vettio’s core strengths must be in list format.

#    - **Candidate Overview**:
#      - For each recommended candidate, provide in bullet points:
#        - A hyperlinked name directing to their profile.
#        - Must add a summary of their experience and key skills.
#        - Must explain why they are a strong fit for the role in details according to requisition.
#        - End this section by noting that these are select examples and hyperlink to a page for exploring more candidates.

#    - **Call to Action & Closing**:
#       - Wrap up with a professional and engaging statement, leaving a positive impression.
#       - Encourage them to explore Vettio’s platform for more candidates and innovative recruitment solutions.

# ## Instructions:
#         - Must explain company strengths and each candidate overviews in list format.
#         - Must include candidate experience in their overview.
#         - Maintain a professional, conversational tone.
#         - Ensure smooth transitions between sections for natural flow.
#         - Avoid using rigid section headings; integrate details seamlessly.
#         - Make sure to include hyperlinks in the anchor tag format, not pure raw hyperlinks.
