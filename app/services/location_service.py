import logging
from services.requisition.requisition import RequisitionServiceClass
from services.country import CountryServiceClass
from services.city import CityServiceClass
from services.state import StateServiceClass

from services.external.openai import OpenAIServiceClass
from sentence_transformers import SentenceTransformer, util

logger = logging.getLogger(__name__)
requisition_service_object = RequisitionServiceClass()
open_ai_service_object = OpenAIServiceClass()
country_service_object = CountryServiceClass()
city_service_object = CityServiceClass()
state_service_object = StateServiceClass()


logger = logging.getLogger(__name__)

class LocationService:

    def __init__(self):
        self.model = SentenceTransformer('sentence-transformers/all-mpnet-base-v2')

    def get_location(self,city,country_code,state,db):

        try:
            logger.info(f"Getting location match started")
            if not country_code:
                logger.error(f"Country code is not provided")
                data={
                    "city_id":0,
                    "state_id":0,
                    "country_id":0,
                    "location":None
                }
                return data
            else:
                country=country_service_object.get_country_by_country_code(country_code,db)
                if not country:
                    logger.error(f"Country not found for code: {country_code}")
                    location_parts = [part for part in [city, state, country_code] if part]
                    location = ", ".join(location_parts)
                    location=self.clean_location(location)
                    data={
                        "city_id":0,
                        "state_id":0,
                        "country_id":0,
                        "location":location
                    }
                    return data
                else:
                    country_id=country["id"]
                    city_data=" "
                    state_data=" "
                    city_id=0
                    state_id=0


                    if(state):
                        state_data=self.match_state(state,country_id,db)
                        if state_data:
                            response=state_service_object.get_state_id(state_data,country["name"],db)
                            state_id=response["state_id"]
                    
                    if(city):
                        city_data=self.match_city(city,country_id,db)
                        if city_data:
                            response=city_service_object.get_city_id(city_data,country["name"],db)
                            city_id=response["city_id"]
                            if not state:
                                state_id=response["state_id"]



                    location_parts = [part for part in [city, state, country["name"]] if part]
                    location = ", ".join(location_parts)
                    location=self.clean_location(location)
                    data={
                        "city_data":city_data,
                        "state_data":state_data,
                        "location":location,
                        "city_id":city_id,
                        "state_id":state_id,
                        "country_id":country_id,
                    }
                    logger.info(f"Location match completed")
                    return data

        except Exception as e:
            logger.error(f"Error in getting country by code: {country_code}, error: {str(e)}")
            raise e
    
    def match_city(self,cand_city,country_id,db):
        try:
            logger.info(f"Matching city started")
            logger.info(f"Country_id{country_id}")
            cities=city_service_object.get_cities_by_country_id(country_id,db)

            if cities:
                query = cand_city  
                docs = [city['name'] for city in cities]

                # Encode query and documents
                query_emb = self.model.encode(query)
                doc_emb = self.model.encode(docs)

                # Compute dot score between query and all document embeddings
                scores = util.dot_score(query_emb, doc_emb)[0].cpu().tolist()

                # Combine docs & scores
                doc_score_pairs = list(zip(docs, scores))

                # Sort by decreasing score
                doc_score_pairs = sorted(doc_score_pairs, key=lambda x: x[1], reverse=True)

                # Return the document with the maximum score
                max_score_doc = doc_score_pairs[0][0]
                logger.info(f"City match completed")
                return max_score_doc
            else:
                logger.info(f"No cities found for country_id: {country_id}")
                return None
        except Exception as e:
            logger.error(f"Error in matching city: {country_id}, error: {str(e)}")
            raise e
        
    def match_state(self,cand_state,country_id,db):
        try:
            logger.info(f"Matching state started")
            states=state_service_object.get_states_by_country_id(country_id,db)
            if states:
                query = cand_state 
                docs = [state['name'] for state in states]

                # Encode query and documents
                query_emb = self.model.encode(query)
                doc_emb = self.model.encode(docs)

                # Compute dot score between query and all document embeddings
                scores = util.dot_score(query_emb, doc_emb)[0].cpu().tolist()

                # Combine docs & scores
                doc_score_pairs = list(zip(docs, scores))

                # Sort by decreasing score
                doc_score_pairs = sorted(doc_score_pairs, key=lambda x: x[1], reverse=True)

                # Return the document with the maximum score
                max_score_doc = doc_score_pairs[0][0]
                logger.info(f"State match completed")
                return max_score_doc
            else:
                logger.info(f"No states found for country_id: {country_id}")
                return None
        except Exception as e:
            logger.error(f"Error in matching state: {country_id}, error: {str(e)}")
            raise e
        
    def clean_location(self,location):
        parts = location.split(',')

        cleaned_parts = [part.strip() for part in parts if part.strip().lower() != 'null']

        if not cleaned_parts:
            return None

        return ','.join(cleaned_parts)
                