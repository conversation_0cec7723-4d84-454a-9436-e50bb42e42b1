from celery_config import celery_app
import json
from services.external.openai import OpenAIServiceClass
from schemas.evaluation_criteria import EvaluationCriteria as EC
from services.requisition.requisition import RequisitionServiceClass
from bs4 import BeautifulSoup
import logging


logger = logging.getLogger(__name__)


class EvaluationCriteria:
    def __init__(self):
        self.openai_service = OpenAIServiceClass()
        self.requisition_service = RequisitionServiceClass()

    def create_evaluation_criteria(self, requisition_id: int, db) -> list[str]:
        """
        Creates evaluation criteria for a given requisition.

        Args:
            requisition_id (int): The ID of the requisition.
            db: The database session or connection.

        Returns:
            bool: True if evaluation criteria are successfully generated, False otherwise.

        Raises:
            Exception: If there is an error during the generation process.
        """

        try:
            logger.info(
                f"Generating evaluation criteria for requisition ID: {requisition_id}")

            requisition = self.requisition_service.get_requisition_by_id(
                requisition_id, db)

            technical_requirements = json.loads(
                requisition.llm_requistion_details)
            binary_requirements = json.loads(requisition.binary_requirements)

            evaluation_criteria = self.openai_service.evaluation_criteria(
                technical_requirements, binary_requirements)
            evaluation_criteria = {
                "criteria": evaluation_criteria["technical_criteria"] + evaluation_criteria["binary_criteria"]
            }

            requisition.evaluation_criteria = evaluation_criteria
            db.commit()
            db.refresh(requisition)

            logger.info(
                f"Successfully generated evaluation criteria for requisition ID: {requisition_id}"
            )

            return evaluation_criteria
        except Exception as e:
            raise e

    def get_evaluation_criteria(self, requisition_id: int, db) -> EC:
        """
        Get the evaluation criteria for a given requisition.

        Args:
            requisition_id (int): The ID of the requisition.
            db: The database session or connection.

        Returns:
            dict: The evaluation criteria for the requisition.
        """

        requisition = self.requisition_service.get_requisition_by_id(
            requisition_id, db)

        return requisition.evaluation_criteria
