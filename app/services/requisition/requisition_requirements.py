from services.external.openai import OpenAIServiceClass
from services.requisition.requisition import RequisitionServiceClass
import logging
from custom_exceptions import EntityNotFoundException
from services.country import CountryServiceClass
from services.city import CityServiceClass
from services.state import StateServiceClass
from services.user.user import UserServiceClass
from services.filemanager.filemanager import FileManagerClass
from services.parser.text_extractor_service import TextExtractorService
from services.qc_automation.qc_automation import QCAutomationClass
from services.external.bedrock import BedrockService
from models.question_feedback import QuestionFeedback
from models.user_evaluations import UserEvaluation
import json

logger = logging.getLogger(__name__)

openai_service = OpenAIServiceClass()
requisition_service = RequisitionServiceClass()
country_service=CountryServiceClass()
state_service=StateServiceClass()
city_service=CityServiceClass()
user_service=UserServiceClass()
file_manager_object = FileManagerClass()
text_extractor_service = TextExtractorService()
qc_automation_service = QCAutomationClass()
bedrock_service = BedrockService()



class RequisitionRequirementsServiceClass:

    def get_requirements(self,req_id,db):
        try:
            logger.info(f"Getting requirements for requisition with id {req_id} has started.")
            requisition=requisition_service.get_requisition_by_id(req_id,db)
            if not requisition:
                logger.error(f"Requisition with id {req_id} not found.")
                raise EntityNotFoundException(f"Requisition with id {req_id} not found.")
            country=country_service.get_country_by_id(requisition.country_id,db)
            city=city_service.get_city_by_id(requisition.city_id,db)
            state=state_service.get_state_by_id(requisition.state_id,db)

            requisition_data={
                "title": requisition.title,
                "description":requisition.description,
                "salary_range":requisition.salary_range,
                "country":country,
                "city":city,
                "state":state,
                "location_type":requisition.location_type,
                "level":requisition.level,
                "salary_details":requisition.salary_details,
                "long_description":requisition.long_description
            }
            result=openai_service.get_parsed_requisition_requirements(requisition_data)
            result=json.dumps(result)

            requisition_service.update_requisition_parsed_requirements(req_id,result,db)
            return result

        except Exception as e:
            logger.error(f"Error getting requirements of requisition with id {req_id}: {str(e)}")
            raise e
        
    def get_requirement_evaluation(self,user_id,requisition_id,db):
        try:
            user=user_service.get_user_by_id(user_id,db)
            resume_link=json.loads(user.resume_link)
            file_path=resume_link["file_path"]
            if not file_path:
                logger.error("File path is invalid")
                return None

            llmResponse = qc_automation_service.scan_resume_and_extract_data(file_path)
            llmResponse=llmResponse['response']
            transcript= self.get_interview_transcript(user_id, db)
            parsed_requirements = requisition_service.get_requisition_parsed_requirements(requisition_id, db)
            evaluation= openai_service.get_requisition_requirements_evaluation(
                resume=llmResponse,
                transcript=transcript,
                parsed_requirements=parsed_requirements
            )
            self.insert_into_user_evaluations(
                user_id=user_id,
                requisition_id=requisition_id,
                feedback=json.dumps(evaluation),
                status=0,
                db=db
            )
        except Exception as e:
            logger.error(f"Error getting requirement evaluation for user {user_id} and requisition {requisition_id}: {str(e)}")
            raise e
        


    def get_interview_transcript(self, user_id, db):

        try:
            logger.info(f"Fetching interview transcript started for user_id: {user_id}")
            question_feedbacks = db.query(QuestionFeedback).filter(
                QuestionFeedback.user_id == user_id).order_by(QuestionFeedback.id.asc()).all()
            separator = '\n----\n'
            nl = '\n'
            transcript = separator.join(
                [f"REQUIREMENT: {question_feedback.requirement}{nl}INTERVIEWER: {question_feedback.question}{nl}CANDIDATE: {question_feedback.answer}"
                 for question_feedback in question_feedbacks])

            return transcript
        except Exception as e:
            logger.error("Error fetching interview transcript")
            logger.error(e)
            raise e
        

    def insert_into_user_evaluations(self,user_id,requisition_id,feedback,status,db):
        try:
            logger.info(f"Inserting user evaluation for user_id: {user_id}, requisition_id: {requisition_id}")
            user_eval_data = UserEvaluation(
                requisition_id=requisition_id,
                user_id=user_id,
                feedback=feedback,
                status=status
            )
            db.add(user_eval_data)
            db.commit()
            db.refresh(user_eval_data)
            logger.info("User evaluation inserted successfully")
            return user_eval_data
        except Exception as e:
            logger.error(f"Error inserting user evaluation: {str(e)}")
            raise e