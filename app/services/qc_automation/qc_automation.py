from models.question_feedback import QuestionFeedback
from schemas.qc_automation import Overview, StrengthsAndWeaknesses,UserEducationInfoFlags, UserExperienceInfoFlags, RequirementsListWithFlag, UserExperienceInfo, FlaggedData, UserDetails
import os
import requests
import json
from tempfile import TemporaryDirectory
import re
from fastapi import HTTPException, status
from fastapi.encoders import jsonable_encoder
from models.user import User
from models.user_education import UserEducation
from models.user_experience import UserExperience
from models.skill import Skill
from models.user_skill import UserSkill
from models.school import School
from models.city import City
from models.country import Country
from models.organization import Organization
from models.interview_feedback import InterviewFeedback
import json
from services.external.openai import OpenAIServiceClass
import logging
from services.interview.interview import InterviewServiceClass
from datetime import datetime
from openai import OpenAI
from helper.resume_parser import pdf_to_images
from services.external.bedrock import BedrockService


logger = logging.getLogger(__name__)
openaiservice = OpenAIServiceClass()
interview_service = InterviewServiceClass()

bedrock_service = BedrockService()

class QCAutomationClass:

    def _fix_overview(self, overview, use_openai=False):

        system_prompt_overview = """You are given an overview of a candidate. The overview contains a detailed assessment of the candidate for an interview, generated by the interviewer. Your task is to carefully analyze the overview and look for any issues, duplicates, broken sentences etc. If such issues persist, you need to set the **flag** to True. Otherwise, set it to False. Furthermore, you should also generated the corrected overview in the format provided.
        The overview is: {overview}
        """

        system_prompt = system_prompt_overview.format(
            overview=overview)
        
        if use_openai:
            client = OpenAI()
            completion = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": system_prompt,
                    }
                ], tools=[
                    {
                        "type": "function",
                        "function": {
                                "name": "Overview",
                                "description": "Candidate's overview with flags",
                                "parameters": Overview.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "Overview"},
                },
                temperature=0.7
            )

            response = json.loads(
                completion.choices[0].message.tool_calls[0].function.arguments)
            logger.info(response)
            return response
        else:
            logger.info("Using Claude...")
            msg=[{'role': 'user', 'content': [{'text': system_prompt}]}]
            response = bedrock_service.llm_json(messages=msg, parameters=Overview, function_description="Candidate's overview with flags")
            return response


    def _fix_strengths_weaknesses(self, data, use_openai=False):
        
        system_prompt_strenghts_weakness = """You are a critical analyzer. Your task is to evaluate interview feedback consisting of two textual inputs: **strengths** and **weaknesses**.
        
        ## BACKGROUND
        The strengths and weaknesses are generated by an interviewer after assessing a candidate's interview performance.

        ## OBJECTIVE
        Your goal is to inspect both sections for quality issues and output flags based on the following rules:

        ### Weaknesses
        Set the `weaknesses_flag` to `True` if:
        1. The text mentions that the interviewee **asks for clarification** or considers **asking for clarification** a weakness.
        2. There are **duplicate points**.
        3. There are any **errors**, such as repeated entries, contradictory points, or structural issues.

        Otherwise, set the `weaknesses_flag` to `False`.

        ### Strengths
        Set the `strengths_flag` to `True` if:
        1. There are **duplicate points**.
        2. There are **formatting issues**, such as broken or missing bullet points, sentence fragments, or inconsistencies.

        Otherwise, set the `strengths_flag` to `False`.

        Make sure to output the corrected strengths and weaknesses.
        The data is {data}
        """

        system_prompt = system_prompt_strenghts_weakness.format(
            data=data)
        
        if use_openai:
            client = OpenAI()
            completion = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": system_prompt,
                    }
                ], tools=[
                    {
                        "type": "function",
                        "function": {
                                "name": "StrengthsAndWeaknesses",
                                "description": "Candidate's strengths and weaknesses with flags",
                                "parameters": StrengthsAndWeaknesses.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "StrengthsAndWeaknesses"},
                },
                temperature=0.7
            )

            response = json.loads(
                completion.choices[0].message.tool_calls[0].function.arguments)
            logger.info(response)
            return response
        else:
            logger.info("Using Claude...")
            msg=[{'role': 'user', 'content': [{'text': system_prompt}]}]
            response = bedrock_service.llm_json(messages=msg, parameters=StrengthsAndWeaknesses, function_description="Candidate's strengths and weaknesses with flags")
            return response

    def _fix_after_evaluation_requirements(self, requirements, use_openai=True):
        
        system_prompt_requirements = """You are a critical analyzer who is given a list of requirements. Each requirement is a dictionary, and each dictionary contains a requirement, the evaluation, the statement\n\n

        **BACKGROUND CONTEXT**
        The requirement is a given job requirement, while the statement is a person's answer to that requirement. The evaluation assesses whether the statement satsifies the requirement. \n\n

        **OBJECTIVE**
        Your objective is to **CAREFULLY** analyze the **evaluation** part. You need to determine whether the evaluation is correctly done against the candidate's statement for a given requirement. If it incorrectly evaluates the candidate's statement against the requirement, then you must set the flag variable to **True**. Otherwise, if the evaluation has no issues and is correct, then you should set it to **False**. 
        Alongwith a flag, you also need to a **flag_reason** key stating as to why you added the particular flag value as either True or False.

        **FEW SHOT EXAMPLES**
        1. For example, if the statement has something talking about Python, but the requirement is related to Javascript, and the evaluation takes note of this issue, then you can safely set the flag to False, since the evaluation is correct.\n
        2. Similarly, if the candidate's statement is not answering the original requirement and the evaluation takes note of this, you can also safely set the flag to False again, since it is mentioned in the evaluation.\n
        3. If the statement includes cases where the candidate is UNAWARE of the topic and thus can't address the requirement, and the evaluation negatively evaluates the candidate, then you can also safely set the flag to False, since the evaluation is correct and the candidate isn't AWARE on the given topic. \n
        4. If the statement includes cases where the candidate is asking for clarification, but the evaluation negatively evaluates this against the requirement, then you MUST set the flag to True, since the evaluation is incorrect. \n
        5. Similarly, if the statement involves candidate not answering the original requirement, but the evaluation doesn't take note of this issue, then you should set the flag to True, since the evaluation didn't take note of this error.\n
        
        **IMPORTANT POINTS**
        1- Any evaluations where there has been mentioned that the candidate or person was not able to understand or hear the question, sets the flag to True. \n 2- Any mention in the evaluation where the candidate might have been negatively evaluated as not being able to hear or comprehend the problem (as that is a common issue and should be ignored) also sets the flag to True. \n3- Any other cases where you think the evaluation is totally incorrect and the flag should be True, you can set it to True. But you should not unncessarily flag the evaluation to True if it correctly eva\n\n

        **OUTPUT KEYS INSTRUCTIONS**
        - Your output **MUST** contain `requirement`, `statement`, `evaluation`, `flag`, and `flag_reason` keys for **EVERY** item in your requirements dictionary.\n      
        
        List of requirements are: {requirements}
        """

        system_prompt = system_prompt_requirements.format(
            requirements=requirements)
        
        if use_openai:
            client = OpenAI()
            completion = client.chat.completions.create(
                        model="gpt-4o",
                        messages=[
                            {
                                "role": "user",
                                "content": system_prompt,
                            }
                        ], tools=[
                            {
                                "type": "function",
                                "function": {
                                        "name": "RequirementsListWithFlag",
                                        "description": "List of requirements containing original content and flags",
                                        "parameters": RequirementsListWithFlag.model_json_schema(),
                                },
                            }
                        ],
                        tool_choice={
                            "type": "function",
                            "function": {"name": "RequirementsListWithFlag"},
                        },
                        temperature=0.1
                    )

            response = json.loads(
                        completion.choices[0].message.tool_calls[0].function.arguments)
            return response

        else: 
            logger.info("Using Claude...")
            msg=[{'role': 'user', 'content': [{'text': system_prompt}]}]
            response = bedrock_service.llm_json(messages=msg, parameters=RequirementsListWithFlag, function_description="List of requirements containing original content and flags")
            return response

        
    def _fix_interview_transcript(self, transcript, use_openai=False):
        
        """Analyze resume images with GPT-4o and return structured data"""
        system_prompt_interview_transcript = """You are given an interview transcript. The transcript contains a series of questions and answers being asked in an interview. YOUR objective is: \n\n
        1. To identify and flag as True, if any question or answer contains things that are not relevant to the interview, otherwise set the flag to False. For example, if the answer says something totally unrelated to the interview question, or if the question is totally irrelevant to the interview, you need to **FLAG** that pair's ID. The flag is TRUE if the answer is blank or empty, or on a different subject. The flag is False if there is no issue with the question or answer. For example, if the question is asking for a given topic but the answer is talking about totally something else unrelated to the question, then flag is True. However, if the answer contains a bit of relevance to the question, then set the flag to False, as we don't want to exclude important information relevant for evaluating the answers in later stages. \n
        2. Also a flag_reason field indicating as to why you set the flag to either True or False. Create a consise and brief reason only.\n
        
        You should also return the question_id as is in your response for each question as well. Each question has a unique question id, its question, and an answer. The transcript is {transcript}""" 
       
        system_prompt = system_prompt_interview_transcript.format(
            transcript=transcript)
        
        if use_openai:
            client = OpenAI()
            completion = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": system_prompt,
                    }
                ], tools=[
                    {
                        "type": "function",
                        "function": {
                                "name": "FlaggedData",
                                "description": "Flagged data from the interview transcript.",
                                "parameters": FlaggedData.model_json_schema(),
                        },
                    }
                ],
                tool_choice={
                    "type": "function",
                    "function": {"name": "FlaggedData"},
                },
                temperature=0.3
            )

            response = json.loads(
                completion.choices[0].message.tool_calls[0].function.arguments)
            return response
        else: 
            msg=[{'role': 'user', 'content': [{'text': system_prompt}]}]
            response = bedrock_service.llm_json(messages=msg, parameters=FlaggedData, function_description="Flagged data from the interview transcript.")
            return response


    def analyze_resume_without_image(self, user_info, use_openai=False):

        system_prompt_fix_user_education_info = """
            ## Objective:
            You are provided with a JSON-like object containing a person's **education** information. Your task is to:

            ### 1. **Identify and Correct Issues in the JSON Object**
            - Replace any field with a value of `"null"` with an **empty string** (`""`).

            ### 2. **Flag Entries Criteria**
            - Add a **flag** (Boolean value: `True` or `False`) to each education entry.
            - Set the flag to `True` if:
                - Any `"null"` or "" (empty string) values are present in the original JSON-like object, set flag to True. EXcept for `to_date`
                - If from_date and to_date both are empty, set flag to True
                - If from_date is empty but to_date is not empty, set flag to True
                - If any education entires are duplicates. This means, that if there are duplicate education entries accross the whole education JSON object, then set the flag to True for both the duplicate entries, and only output a **single** entry since they are duplicates
            - Set the flag to `False` otherwise in all cases.
            - Set flag to `False` also if from_date is present, but to_date is not given, as this would indicate the person is currently studying there

            ### 3. **Flag Reason""
            - For every flag you added, also state a consise reason as to why that particular flag is either True or False.

            ### 4. **SORTING EDUCATION FROM LATEST TO OLDEST**
            - You also need to make sure to **SORT** the education entries such that recent educations are shown first, so in a chronological descending order

            ### 5. **Do Not Add or Invent Information**
            - Only fix what's provided. Do not add new content or make assumptions.

            ### Output:
            - Return the updated education entries.
            - Include a flag for each entry indicating whether corrections were made (`True`) or not (`False`).

            Education JSON object is: {education_info}
        """

        system_prompt_fix_user_experience_info = """
            ## Objective:
            You are provided with a JSON-like object containing a person's **work experience** information. Your task is to:

            ### 1. **Identify and Correct Issues in the JSON Object**
            - Check each experience entry for any **grammatical errors** or **typos**.
            - Replace any field with a value of `"null"` with an **empty string** (`""`).

            ### 2. **Flag Entries Criteria**
            - Add a **flag** (Boolean value: `True` or `False`) to each experience entry.
            - Set the flag to `True` if:
                - Any `"null"` or "" (empty string) values are present in the original JSON-like object, set flag to True. EXcept for `to_date`
                - If from_date and to_date both are empty, set flag to True
                - If from_date is empty but to_date is not empty, set flag to True
                - If any education entires are duplicates. This means, that if there are duplicate education entries accross the whole experience JSON object, then set the flag to True for both the duplicate entries, and only output a **single** entry since they are duplicates.
            - Set the flag to `False` otherwise in all cases.
            - Set flag to `False` also if from_date is present, but to_date is not given, as this would indicate the person is currently working there

            ### 3. **Flag Reason""
            - For every flag you added, also state a consise reason as to why that particular flag is either True or False.

            ### 4. **SORTING EXPERIENCE FROM LATEST TO OLDEST**
            - You also need to make sure to **SORT** the experience entries such that recent experiences are shown first, so in a chronological descending order

            ### 5. **Do Not Add or Invent Information**
            - Only fix what's provided. Do not add new content or make assumptions.

            ### Output:
            - Return the updated experience entries.
            - Include a flag for each entry indicating whether corrections were made (`True`) or not (`False`).

            Experience JSON object is: {experience_info}
        """

        # Separate education and experience data
        # Separate education and experience data
        education_dict = {k: v for k, v in user_info.items() if k != "work_experience_list"}
        # logger.info(education_dict)

        experience_dict = {"work_experience_list": user_info.get("work_experience_list", [])}


        # Format prompts
        system_prompt_edu = system_prompt_fix_user_education_info.format(education_info=education_dict)
        system_prompt_exp = system_prompt_fix_user_experience_info.format(experience_info=experience_dict)

        # Use OpenAI path
        if use_openai:
            client = OpenAI()

            # Education
            completion_edu = client.chat.completions.create(
                model="gpt-4o",
                messages=[{"role": "user", "content": system_prompt_edu}],
                tools=[{
                    "type": "function",
                    "function": {
                        "name": "UserDetails",
                        "description": "Candidate's user details.",
                        "parameters": UserDetails.model_json_schema(),
                    },
                }],
                tool_choice={"type": "function", "function": {"name": "UserDetails"}},
                temperature=0.1,
            )
            response_edu = json.loads(
                completion_edu.choices[0].message.tool_calls[0].function.arguments
            )

            # Experience
            completion_exp = client.chat.completions.create(
                model="gpt-4o",
                messages=[{"role": "user", "content": system_prompt_exp}],
                tools=[{
                    "type": "function",
                    "function": {
                        "name": "UserExperienceInfo",
                        "description": "Candidate's experience details.",
                        "parameters": UserExperienceInfo.model_json_schema(),
                    },
                }],
                tool_choice={"type": "function", "function": {"name": "UserExperienceInfo"}},
                temperature=0.1,
            )
            response_exp = json.loads(
                completion_exp.choices[0].message.tool_calls[0].function.arguments
            )

            response_exp = response_exp.get('work_experience', [])

            response = {"user_info_and_education": response_edu, "work_experience": response_exp}
            return response

        # Use Bedrock path
        else:
            # Education
            messages_edu = [{'role': 'user', 'content': [{'text': system_prompt_edu}]}]
            response_edu = bedrock_service.llm_json(
                messages=messages_edu,
                parameters=UserDetails,
                function_description="Candidate's user details."
            )

            # Experience
            messages_exp = [{'role': 'user', 'content': [{'text': system_prompt_exp}]}]
            response_exp = bedrock_service.llm_json(
                messages=messages_exp,
                parameters=UserExperienceInfo,
                function_description="Candidate's experience details."
            )
            response_exp = response_exp.get('work_experience', [])

            response = {"user_info_and_education": response_edu, "work_experience": response_exp}
            logger.info("Processed via Bedrock Claude")
            return response

    def _fix_personal_details_section_without_image(self, user_info):

        try:        
            logger.info("Starting user info QC process...")
            response = self.analyze_resume_without_image(user_info)

            return response
        
        except Exception as e:
            return {"error": f"Error during resume processing: {str(e)}"}

    def scan_resume_and_extract_data(self, resume_url):

        if not resume_url:
            return {"error": "Resume URL not found in user_info."}

        logger.info(f"Resume file URL: {resume_url}")

        try:
            with TemporaryDirectory() as temp_dir:
                temp_pdf_path = os.path.join(temp_dir, "resume.pdf")

                response = requests.get(resume_url)
                response.raise_for_status()
                with open(temp_pdf_path, 'wb') as f:
                    f.write(response.content)
                image_output_dir = os.path.join(temp_dir, "images")
                image_paths = pdf_to_images(temp_pdf_path, output_dir=image_output_dir)

                response = bedrock_service.analyze_resume(image_paths)

                return {"success": True, "image_paths": image_paths, "response": response}

        except requests.exceptions.RequestException as e:
            logger.error(str(e))
            raise e
        except Exception as e:
            logger.error(str(e))
            raise e
    
    # Function to parse total experience string
    def parse_total_experience(self, exp_str):
            if not exp_str or exp_str == "_":
                return 0
            match = re.match(r"(\d+)(?:\+)?(?:\s*years)?(?:\s*(\d+)\s*months)?", exp_str)
            if match:
                years = int(match.group(1))
                months = int(match.group(2)) if match.group(2) else 0
                total_years = years + months / 12.0
                return round(total_years)  # This will round 2.5 to 3, 2.4 to 2, etc.
            return 0

    def calculate_experience_years(self, from_date, to_date=None):
        """Calculate experience in decimal years based on months of difference."""
        if not from_date:
            return 0.0
        if not to_date:
            to_date = datetime.today()

        # Calculate total months difference
        months_diff = (to_date.year - from_date.year) * 12 + (to_date.month - from_date.month)
        # If days are involved, adjust months accordingly
        if to_date.day < from_date.day:
            months_diff -= 1  # Round down if the day in to_date is before from_date

        # Convert months to years in decimal
        return round(max(0, months_diff / 12), 2)
    
    def convert_data(self, user_id, db):
        # Fetch user details
        user = db.query(
            User.id, User.first_name, User.last_name, User.email, User.total_experience, User.city_id, User.expected_salary, User.notice_period, User.location_preferences, User.phone, User.resume_link, User.location
        ).filter(User.id == user_id).first()
      
        if not user:
            return None

        # Check email presence
        email = user.email if user.email else None
        expected_salary = user.expected_salary
        location_preferences = user.location_preferences
        notice_period = user.notice_period
        phone = user.phone
        location = user.location
        first_name = user.first_name
        last_name = user.last_name

        # Fetch skills
        skill_names = [
            skill[0] for skill in db.query(Skill.name)
            .filter(Skill.id.in_(
                [s[0] for s in db.query(UserSkill.skill_id).filter(UserSkill.user_id == user.id).all()]
            )).all()
        ]
        skills_text = ", ".join(skill_names)

        # Fetch education records
        education_list = []
        for education in db.query(
            UserEducation.id, UserEducation.degree, UserEducation.field, UserEducation.description,
            UserEducation.start_date, UserEducation.end_date, UserEducation.school_id, UserEducation.city_id, UserEducation.location
        ).filter(UserEducation.user_id == user.id).all():
          
            school = db.query(School.name).filter(School.id == education.school_id).first()
            education_list.append({
                "degree": education.degree,
                "field": education.field,
                "description": education.description,
                "from_date": education.start_date,
                "to_date": education.end_date,
                "school": school.name if school else None,
            })


        work_experiences = db.query(
            Organization.name.label("organization"),  # Fetch organization name instead of ID
            UserExperience.job_title,
            UserExperience.from_date,
            UserExperience.to_date,
            UserExperience.emp_type,
            UserExperience.description,
            UserExperience.city_id,  # Include city_id
        ).join(
            Organization, UserExperience.organization_id == Organization.id  # Proper join condition
        ).filter(
            UserExperience.user_id == user.id
        ).all()


        work_experience_list = []
        for exp in work_experiences:
            # city = db.query(City.name, City.country_id).filter(City.id == exp.city_id).first() if exp.city_id else None
            # country = db.query(Country.name).filter(Country.id == city.country_id).first() if city and city.country_id else None

            work_experience_list.append({
            "organization": exp.organization if exp.organization else "",
            "job_title": exp.job_title,
            "from_date": exp.from_date,
            "to_date": exp.to_date,
            "emp_type": exp.emp_type,
            "description": exp.description,
            })


        # Fetch the first feedback entry
        feedback_record = db.query(InterviewFeedback.interview_id, InterviewFeedback.feedback, InterviewFeedback.id)\
            .filter(InterviewFeedback.user_id == user.id).first()

        feedback_list = []
        skills_list = []
        strengths = {}
        evaluation = {}
        weaknesses = {}
        overview = {}
        interview_id = None  # Ensure interview_id is initialized

        # Check if feedback exists
        if feedback_record:
            interview_id = feedback_record.interview_id  # Capture the interview ID
            if feedback_record.feedback:
                try:
                    feedback_data = json.loads(feedback_record.feedback)
                    
                    # Extract requirements
                    feedback_list.extend([
                        {
                            "requirement": req.get("requirement"),
                            "evaluation": req.get("evaluation"),
                            "statement": req.get("statement"),
                            "is_unaddressed": req.get("is_unaddressed"),
                            "percentage": req.get("percentage"),
                            "is_binary": req.get("is_binary"),
                            "weightage": req.get("weightage")
                        }
                        for req in feedback_data.get("requirements", [])
                    ])
                    
                    # Extract skills
                    skills_list.extend([
                        {
                            "evaluation": skill.get("evaluation"),
                            "statement": skill.get("statement"),
                            "is_unaddressed": skill.get("is_unaddressed"),
                            "percentage": skill.get("percentage")
                        }
                        for skill in feedback_data.get("skills", [])
                    ])
                    
                    # Extract strengths, weaknesses, and overview
                    strengths = feedback_data.get("strengths", {})
                    weaknesses = feedback_data.get("weaknesses", {})
                    evaluation = feedback_data.get("evaluation", {})
                    
                except json.JSONDecodeError:
                    pass  # Skip invalid JSON
        else:
            logger.info(f"No feedback found for user: {user_id}, skipping")

        # If feedback is empty, return None
        if not skills_list and not strengths and not weaknesses:
            logger.info(f"No feedback found for user: {user_id}, skipping")

        total_experience = self.parse_total_experience(user.total_experience)

        user_info = {
            "email":email,
            "phone":phone,
            "location": location,
            "total experience": total_experience,
            "first_name": first_name,
            "last_name": last_name,
            "expected_salary": expected_salary,
            "location_preferences": location_preferences,
            "notice_period": notice_period,
            "work_experience_list":work_experience_list,
            "education_list": education_list
        }

        transcript_original=None
        updated_transcript=None
        # if interview_id:
        #     question_feedbacks = db.query(QuestionFeedback).filter(
        #         QuestionFeedback.interview_id == interview_id).order_by(QuestionFeedback.id.asc()).all()
        #     if question_feedbacks:
        #         # Create a dictionary for the transcript with IDs included
        #         transcript = {
        #             f"question_{index + 1}": {
        #             "id": question_feedback.id,
        #             "question": question_feedback.question,
        #             "answer": question_feedback.answer
        #             }
        #             for index, question_feedback in enumerate(question_feedbacks)
        #         }
        #         transcript_original = transcript
        #         updated_transcript = self._fix_interview_transcript(transcript_original)
        #         # Extract the list directly
        #         flag_data_list = updated_transcript.get("data", [])
        #         # Build a lookup dictionary from question_id to flag
        #         flag_lookup = {}
        #         for item in flag_data_list:
        #             try:
        #                 question_id = int(item["question_id"])
        #                 flag_lookup[question_id] = item["flag"]
        #             except (ValueError, TypeError):
        #                 logger.warning(f"Invalid question_id found in flag data: {item['question_id']}")
        
        final_response=[]
        # if feedback_list:
        #     filtered_feedback = [
        #         {
        #             'requirement': item['requirement'],
        #             'evaluation': item['evaluation'],
        #             'statement': item['statement'],
        #         }
        #         for item in feedback_list
        #     ]
        #     updated_requirements = self._fix_after_evaluation_requirements(filtered_feedback)
        #     requirements_list = updated_requirements.get('list_of_requirements_with_flags', [])
        #     final_response = [
        #         {
        #             **item,  # Unpack the processed data
        #             **next((x for x in feedback_list if x['requirement'] == item['requirement']), {})  # Find the original entry and merge back the unnecessary fields
        #         }
        #         for item in requirements_list
        #     ]

       
        updated_user_info = self._fix_personal_details_section_without_image(user_info)

        # updated_overview=None
        # strengths_and_weaknesses=None
        
        # if strengths and weaknesses:
        #     strengths_and_weaknesses = self._fix_strengths_weaknesses({"strengths":strengths, "weaknesses":weaknesses})

        # if evaluation.get('overview'):
        #     updated_overview = self._fix_overview(evaluation.get('overview'))

        return {
            "original_data":{
                "user_info":user_info,
                # "feedback": feedback_list,
                # "transcript": transcript_original,
                # "strengths and weaknesses":{"strengths":strengths, "weaknesses":weaknesses},
                # "overview":evaluation.get("overview", "")
            },
            "updated_data":{
                "updated_user_info":updated_user_info,
                # "updated_feedback":final_response,
                # "updated_transcript":updated_transcript,
                # "updated_strengths_and_weaknesses":strengths_and_weaknesses,
                # "updated_overview": updated_overview
            }
        }  

    def get_user_data(self, user_id, db):
        user_data = self.convert_data(user_id, db)
        if user_data:
            return jsonable_encoder(user_data)
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
        

