from services.external.opensearch import OpenSearchServiceClass
from typing import Dict, Any
import logging
import re
import numpy as np
from dependencies import OPENSEARCH_CPA_INDEX, OPENSEARCH_REQUISITION_EMBEDDING_INDEX
from services.external.openai import OpenAIServiceClass

open_ai_service_object = OpenAIServiceClass()

logger = logging.getLogger(__name__)

class PreEvalServiceClass:

    def convert_resume_to_opensearch_format_and_insert(self, llm_response, email):
        try:
                    # Function to parse total experience string
            logger.info(f"Converting resume to OpenSearch format and inserting")


            
            opensearch_doc = {
                "person_id": " ",
                "name": llm_response.get("name", ""),
                "email": email,
                "total_experience": 0,
                "skills": llm_response.get("skills", []),
                "tools": llm_response.get("tools", []),
                # "certifications": llm_response.get("certifications", []),
                "linkedin": llm_response.get("linkedin", ""),
                "number": llm_response.get("number", ""),
                "summary": llm_response.get("summary", ""),
                "address": llm_response.get("address", {
                    "street": "",
                    "city": "",
                    "state": "",
                    "zip": "",
                    "country": ""
                }),
                "education": [],
                "projects": [],
                "work_experience": []
            }

            # Process nested education
            for edu in llm_response.get("education", []):
                opensearch_doc["education"].append({
                    "school": edu.get("school", ""),
                    "degree": edu.get("degree", ""),
                    "field": edu.get("field", ""),
                    "description": edu.get("description", ""),
                    "grade": edu.get("grade", ""),
                    "city": edu.get("city", ""),
                    "country": edu.get("country", ""),
                    "start_date": "1900-01-01",
                    "end_date": "1900-01-01"
                })

            # Process nested projects
            for proj in llm_response.get("projects", []):
                opensearch_doc["projects"].append({
                    "name": proj.get("name", ""),
                    "description": proj.get("description", ""),
                    "duration": proj.get("duration", "")
                })

            # Process nested work experience
            for work in llm_response.get("work_experience", []):
                opensearch_doc["work_experience"].append({
                "organization": work.get("organization", ""),
                "job_title": work.get("job_title", ""),
                "emp_type": work.get("emp_type", ""),
                "location_type": work.get("location_type", ""),
                "description": work.get("description", ""),
                "start_date": "1900-01-01",  # Default date
                "end_date": "1900-01-01",      # Default date
                "city": work.get("city", ""),
                "country": work.get("country", "")
                })


                skills = ", ".join(llm_response.get("skills", []))


                work_experience = "\n".join([
                    f"Job title: {exp['job_title']}, working at {exp['organization']}. Start date is ({exp['start_date']} and end date is {exp['end_date']})\n"
                    f"Employment type: {exp['emp_type']}, Location: {exp['location_type']}\n"
                    f"Description: {exp['description']}"
                    for exp in llm_response.get("work_experience", [])
                ])
                work_experience_embeddings = OpenAIServiceClass().get_embeddings(text=work_experience)['vector']
                skills_embeddings = OpenAIServiceClass().get_embeddings(text=skills)['vector']

                opensearch_doc["embeddings"] = {
                    "skills": skills_embeddings,
                    "work_experience": work_experience_embeddings,
                }
            logger.info(f"Inserting document to OpenSearch: {opensearch_doc}")
            opensearch_service = OpenSearchServiceClass(index_name=OPENSEARCH_CPA_INDEX)
            result = opensearch_service.insert_to_opensearch_auto_id(opensearch_doc)
            logger.info(f"RESULTTTTTTP{result}")
            return result
        except Exception as e:
            logger.error(f"Error in convert_resume_to_opensearch_format_and_insert: {e}")
            raise e
    

    def get_similarity(self,req_id,email):
        try:
            logger.info(f"Get similarity started with requisition id: {req_id} and email: {email}")
            embeddings = OpenSearchServiceClass(OPENSEARCH_REQUISITION_EMBEDDING_INDEX).get_single_document(req_id)
            result = OpenSearchServiceClass(OPENSEARCH_CPA_INDEX).get_similar_cand_JD_matching(email,np.array(embeddings["embeddings"]))
            return result
        except Exception as e:
            logger.error(f"Error in get_similarity: {e}")
            raise e
