import logging
from services.external.opensearch import OpenSearchServiceClass
from services.external.openai import OpenAIServiceClass
from dependencies import OPENSEARCH_RC_INDEX

logger = logging.getLogger(__name__)


class BulkEducationService:
    """
    Service for handling bulk education embedding operations.
    Separates bulk processing logic from basic OpenSearch CRUD operations.
    """
    
    def __init__(self):
        self.opensearch_service = OpenSearchServiceClass(OPENSEARCH_RC_INDEX)
    
    def get_all_user_ids(self):
        """
        Get all user IDs from OpenSearch for batch processing.
        Returns a list of person_ids.
        """
        try:
            logger.info("Getting all user IDs from OpenSearch for bulk processing")
            
            user_ids = []
            
            # Search body to get only person_id field
            search_body = {
                "query": {"match_all": {}},
                "size": 1000,  # Larger batch for ID collection
                "_source": ["person_id"]
            }
            
            # Initialize scroll
            response = self.opensearch_service.opensearch_client.search(
                index=OPENSEARCH_RC_INDEX,
                body=search_body,
                scroll='5m'
            )
            
            scroll_id = response['_scroll_id']
            hits = response['hits']['hits']
            
            while hits:
                # Extract person_ids from current batch
                for hit in hits:
                    person_id = hit['_source'].get('person_id')
                    if person_id:
                        user_ids.append(person_id)
                
                # Get next batch
                response = self.opensearch_service.opensearch_client.scroll(
                    scroll_id=scroll_id,
                    scroll='5m'
                )
                hits = response['hits']['hits']
            
            # Clear scroll context
            self.opensearch_service.opensearch_client.clear_scroll(scroll_id=scroll_id)
            
            logger.info(f"Retrieved {len(user_ids)} user IDs from OpenSearch")
            return user_ids
            
        except Exception as e:
            logger.error(f"Error getting user IDs from OpenSearch: {e}")
            return []
    
    def process_education_embeddings_batch(self, user_ids_batch):
        """
        Process education embeddings for a batch of user IDs.
        This method will be called by worker celery tasks.
        
        Parameters:
        - user_ids_batch: List of user IDs to process
        """
        try:
            logger.info(f"Processing education embeddings for batch of {len(user_ids_batch)} users")
            
            processed = 0
            updated = 0
            
            for person_id in user_ids_batch:
                try:
                    # Step 1: Get the document from OpenSearch
                    response = self.opensearch_service.opensearch_client.search(
                        index=OPENSEARCH_RC_INDEX,
                        body={
                            "query": {"term": {"person_id": person_id}},
                            "_source": ["education", "embeddings"]
                        }
                    )
                    
                    hits = response.get("hits", {}).get("hits", [])
                    if not hits:
                        logger.debug(f"No document found for person_id={person_id}")
                        processed += 1
                        continue
                    
                    doc_id = hits[0]["_id"]
                    source = hits[0]["_source"]
                    education_data = source.get("education", [])
                    existing_embeddings = source.get("embeddings", {})
                    
                    # Skip if education embedding already exists
                    if "education" in existing_embeddings:
                        logger.debug(f"Education embedding already exists for person_id={person_id}")
                        processed += 1
                        continue
                    
                    # Skip if no education data
                    if not education_data:
                        logger.debug(f"No education data for person_id={person_id}")
                        processed += 1
                        continue
                    
                    # Step 2: Process education data
                    education_texts = []
                    for education in education_data:
                        education_parts = []
                        
                        # Add school name if available
                        if education.get('school'):
                            education_parts.append(f"Field: {education['field']}")
                        
                        # Add description if available
                        if education.get('description'):
                            education_parts.append(f"Description: {education['description']}")
                        
                        if education_parts:
                            education_text = ". ".join(education_parts)
                            education_texts.append(education_text)
                    
                    if not education_texts:
                        logger.debug(f"No meaningful education text for person_id={person_id}")
                        processed += 1
                        continue
                    
                    # Step 3: Create education paragraph
                    education_paragraph = ". ".join(education_texts) + "."
                    
                    # Step 4: Generate embedding
                    openai_service = OpenAIServiceClass()
                    embedding_result = openai_service.get_embeddings(education_paragraph)
                    
                    if not embedding_result:
                        logger.error(f"Failed to generate embedding for person_id={person_id}")
                        processed += 1
                        continue
                    
                    # Step 5: Update document using OpenSearch service
                    self.opensearch_service.opensearch_client.update(
                        index=OPENSEARCH_RC_INDEX,
                        id=doc_id,
                        body={
                            "script": {
                                "source": """
                                    if (ctx._source.embeddings == null) {
                                        ctx._source.embeddings = [:];
                                    }
                                    ctx._source.embeddings.education = params.education_embedding;
                                """,
                                "params": {
                                    "education_embedding": embedding_result["vector"]
                                }
                            }
                        }
                    )
                    
                    logger.info(f"Successfully added education embedding for person_id={person_id}")
                    updated += 1
                    
                except Exception as e:
                    logger.error(f"Error processing person_id={person_id}: {e}")
                
                processed += 1
            
            logger.info(f"Batch completed. Processed: {processed}, Updated: {updated}")
            return {"processed": processed, "updated": updated}
            
        except Exception as e:
            logger.error(f"Error processing education embeddings batch: {e}")
            return {"error": str(e)}
