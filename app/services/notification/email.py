from fastapi import BackgroundTasks
from dependencies import <PERSON><PERSON>_CONFIG
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig
from jinja2 import Environment, FileSystemLoader
import logging
from sendgrid import SendGridAPIClient
from ics import Calendar, Event, Attendee
from sendgrid.helpers.mail import (
    Mail,
    Attachment,
    FileContent,
    FileName,
    FileType,
    Disposition,
    Personalization,
    To,
    CustomArg,
)
import base64
from pytz import timezone, UTC
from datetime import datetime


logger = logging.getLogger(__name__)

env = Environment(loader=FileSystemLoader("templates/email"))

conf = ConnectionConfig(
    MAIL_USERNAME=MAIL_CONFIG["MAIL_USERNAME"],
    MAIL_PASSWORD=MAIL_CONFIG["MAIL_PASSWORD"],
    MAIL_FROM=MAIL_CONFIG["MAIL_FROM"],
    MAIL_PORT=MAIL_CONFIG["MAIL_PORT"],
    MAIL_SERVER=MAIL_CONFIG["MAIL_SERVER"],
    MAIL_FROM_NAME=MAIL_CONFIG["MAIL_FROM_NAME"],
    MAIL_STARTTLS=True,
    MAIL_SSL_TLS=False,
    USE_CREDENTIALS=True,
)


async def send_email_async(subject: str, email_to: str, data: dict, html_template: str):
    try:
        template = env.get_template(html_template)
        email_content = template.render(data)
        message = MessageSchema(
            subject=subject,
            recipients=[email_to],
            body=email_content,
            subtype="html",
        )

        fm = FastMail(conf)
        await fm.send_message(message)
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")

# we will use this function to send email in background

def generate_calendar_invite(data: dict, email_to: str) -> str:
    """
    Generates an iCalendar invite and returns the base64 encoded content.
    """
    calendar = Calendar()
    event = Event()
    event.name = f"Interview Scheduled For {data['job_title']}"

    local_tz = timezone(data['time_zone'])
    event_start_local = datetime.strptime(f"{data['date']}T{data['time']}", '%Y-%m-%dT%H:%M:%S')
    
    # Convert the local time to UTC
    event_start_utc = local_tz.localize(event_start_local).astimezone(UTC)
    event_start =  event_start_utc.strftime('%Y-%m-%d %H:%M:%S')

    event.begin = event_start

    event.description = f"Interview for the {data['job_title']} position"

    organizer = Attendee(email=MAIL_CONFIG["MAIL_FROM"], common_name=data["organization_name"], rsvp="TRUE", role="CHAIR")
    event.organizer = organizer

    calendar.events.add(event)

    ics_content = str(calendar)
    encoded_ics_content = base64.b64encode(ics_content.encode('utf-8')).decode('utf-8')

    return encoded_ics_content

def send_email_background(
    background_tasks: BackgroundTasks,
    subject: str,
    email_to: str,
    data: dict,
    html_template: str,
    sender_email: str = MAIL_CONFIG["MAIL_FROM"],
    cc_emails: dict = None,
    reply_to_email = MAIL_CONFIG["MAIL_FROM"]
):
    try:
        logger.info(f"Sending Email {html_template} to {email_to}")
        template = env.get_template(html_template)
        from_email = f"{MAIL_CONFIG['MAIL_FROM_NAME']} <{sender_email}>"
        
        data["app_name"] = MAIL_CONFIG["MAIL_FROM_NAME"]
        
        message = Mail(
            from_email=from_email,
            to_emails=email_to,
            subject=subject,
            html_content=template.render(data),
        )
        
        # Add reply-to if provided
        if reply_to_email:
            message.reply_to = To(email=MAIL_CONFIG["MAIL_FROM"])

        # Create a Personalization object for the recipient
        personalization = Personalization()
        personalization.add_to(To(email=email_to))
        
        personalization.add_custom_arg(CustomArg("subject", str(subject)))
        message.add_personalization(personalization)

        if 'calendar_invite' in data and data['calendar_invite']:
            ics_content = generate_calendar_invite(data, email_to)
            # Attach calendar invite
            attachment = Attachment(
                FileContent(ics_content),  # Ensure it's a string
                FileName('invite.ics'),
                FileType('text/calendar'),
                Disposition('attachment')
            )

            message.attachment = attachment

        sg = SendGridAPIClient(MAIL_CONFIG["SENDGRID_API_KEY"])
        background_tasks.add_task(sg.send(message), message)
        logger.info(f"Email Sent Response ")
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")


def send_email_with_celery(
    subject: str,
    email_to: str,
    data: dict,
    html_template: str,
    sender_email: str = MAIL_CONFIG["MAIL_FROM"],
    cc_emails: dict = None,
    reply_to_email = MAIL_CONFIG["MAIL_FROM"]
):
    try:
        logger.info(f"Sending Email {html_template} to {email_to}")
        template = env.get_template(html_template)
        from_email = f"{MAIL_CONFIG['MAIL_FROM_NAME']} <{sender_email}>"
        message = Mail(
            from_email=from_email,
            to_emails=email_to,
            subject=subject,
            html_content=template.render(data),
        )
        
        # Add reply-to if provided
        message.reply_to = reply_to_email
            
        if cc_emails:
            for cc_email in cc_emails:
                message.add_cc(To(email=cc_email))

        # Create a Personalization object for the recipient
        personalization = Personalization()
        personalization.add_to(To(email=email_to))

        personalization.add_custom_arg(CustomArg("subject", str(subject)))
        message.add_personalization(personalization)

        sg = SendGridAPIClient(MAIL_CONFIG["SENDGRID_API_KEY"])
        response = sg.send(message)
        logger.info(f"Email Sent Response {response}")
        return response
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")
        raise e