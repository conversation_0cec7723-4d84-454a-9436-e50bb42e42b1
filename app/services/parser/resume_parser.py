from fastapi import Request, HTTPException
import fitz
from pprint import pprint
from services.filemanager.filemanager import FileManagerClass
from services.external.openai import OpenAIServiceClass
from services.user.user import UserServiceClass
from services.parser.text_extractor_service import TextExtractorService
from helper.helper import (
    get_user_education_object,
    get_user_experience_object
)
from dependencies import (
    AWS_RESUME_FOLDER,
)
import json
import logging
from services.location_service import LocationService


logger = logging.getLogger(__name__)

location_service_object = LocationService()


class ResumeParserService:
    def __init__(self):
        pass

    @staticmethod
    def parse(file):
        try:
            temp_file_path = f"temp_{file.filename}"
            print("================================", temp_file_path)
            with open(temp_file_path, "wb") as temp_file:
                content = file.read()
                print("================================", content)
                temp_file.write(content)

            # Open the PDF file
            doc = fitz.open(temp_file_path)
            text = ""
            for page in doc:
                text += page.get_text()

            return str(text)
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
        finally:
            doc.close()


    def parse_resume(self, file_path, file_name_object):
        text_extractor_service = TextExtractorService()
        user_service_object = UserServiceClass()

        # if not text_extractor_service.is_supported_extension(file.filename):
        # raise e
        try:
            logger.info("Upload Resume Service Started")
            file_manager_object = FileManagerClass()

            # store file in temp folder
            # temp_file_path = file_manager_object.save_temp_file(file)
            # file_path = temp_file_path.get("filePath")
            logger.info(f"File Path {file_path}")

            # Check if file path is valid
            if not file_path:
                raise e

            # Extract text from the file
            parser_text = text_extractor_service.extract_text(
                file_path=file_path)
            # Check if parser text is valid
            if not parser_text:
                logger.error("Error in extracting text from the file")

            # Use OpenAI to extract fields from the text
            open_ai_service_object = OpenAIServiceClass()
            llmResponse = open_ai_service_object.extract_fields_from_text(
                parser_text)

            # print("LLM response is", llmResponse)

            return llmResponse

        except Exception as e:
            logger.error("Error in uploading resume")
            logger.error(str(e))
    

    def upload_resume(self, request, file_path, file_name_object, db):
        text_extractor_service = TextExtractorService()
        user_service_object = UserServiceClass()

        # if not text_extractor_service.is_supported_extension(file.filename):
        # raise e
        try:
            logger.info("Upload Resume Service Started")
            file_manager_object = FileManagerClass()

            # store file in temp folder
            # temp_file_path = file_manager_object.save_temp_file(file)
            # file_path = temp_file_path.get("filePath")
            logger.info(f"File Path {file_path}")

            # Check if file path is valid
            if not file_path:
                raise e

            # Extract text from the file
            parser_text = text_extractor_service.extract_text(
                file_path=file_path)
            # Check if parser text is valid
            if not parser_text:
                logger.error("Error in extracting text from the file")

            # Use OpenAI to extract fields from the text
            open_ai_service_object = OpenAIServiceClass()
            llmResponse = open_ai_service_object.extract_fields_from_text(
                parser_text)

            total_experience = llmResponse["total_experience"]
            city = llmResponse.get("address", {}).get("city", None)
            country_code = llmResponse.get("address", {}).get("country", None)
            state= llmResponse.get("address", {}).get("state", None)
            logger.info("================================ LLM Response ================================ ")
            logger.info(str(llmResponse))
            # update user education data
            user_education_object = get_user_education_object(
                llmResponse["education"], db
            )
            logger.info(
                "================================ User Education Object ================================ "
            )
            logger.info(str(user_education_object))
            update_education = user_service_object.update_education(
                user_education_object, request, db
            )
            logger.info(
                "================================ User Education Model ================================ "
            )
            logger.info(str(update_education))
            # update user experience
            user_experience_object = get_user_experience_object(
                llmResponse["work_experience"], db
            )
            logger.info(
                "================================ User Experience Object ================================ "
            )
            logger.info(str(user_experience_object))
            user_experience = user_service_object.update_experience(
                user_experience_object, request, db
            )
            logger.info(
                "================================ User Experience Model ================================ "
            )
            logger.info(str(user_experience))
            # update user skills
            skill = user_service_object.create_update_user_skill(
                llmResponse["skills"], request, db
            )

            data=location_service_object.get_location(city, country_code, state, db)
            
            user_object_to_update = {
                "resume_link": json.dumps(file_name_object),
                "total_experience": total_experience,
                "city_id": data["city_id"],
                "state_id": data["state_id"],
                "country_id": data["country_id"],
                "location": data["location"],
                "about": llmResponse["summary"],
            }

            # update user with resume link
            user_object = user_service_object.update_user_resume_detail(
                user_object_to_update, request, db
            )

            # delete file in temp folder
            file_manager_object.delete_temp_file(file_path)
            return user_object
        except Exception as e:
            logger.error("Error in uploading resume")
            logger.error(str(e))
