"""
document_text_extractor.py

Author: <PERSON><PERSON> <<EMAIL>>

This module provides a class, DocumentTextExtractor, which offers functionality to extract text from DOCX files. It is designed to be utilized as part of a larger application that requires text extraction capabilities from document files.

Dependencies:
- docx2txt: For extracting text from DOCX files.

Usage:
    To use this class, ensure you have the dependencies installed by running:
    `pip install docx2txt`

    Then, create an instance of the DocumentTextExtractor class and call the `extract_text_from_docx` method asynchronously, passing the path to your DOCX file as an argument.
    Example:
        from document_text_extractor import DocumentTextExtractor

        async def main():
            docx_path = 'path/to/your/file.docx'
            text = await DocumentTextExtractor.extract_text(docx_path)
            print(text)

        asyncio.run(main())

Public Methods:
    - extract_text_from_docx(docx_path): Asynchronously extracts text from the specified DOCX file.

Note: This module requires the installation of its dependencies for full functionality.
"""
from typing import Optional
import asyncio
import docx2txt
from docx import Document
import io

class DocumentTextExtractor:
    @staticmethod
    def extract_text(docx_path: str) -> Optional[str]:
        """
        Asynchronously extracts text from a DOCX file.

        This method utilizes the docx2txt library to extract text from the specified DOCX file. It runs the extraction process in a separate thread to avoid blocking the main event loop.

        Args:
            docx_path (str): Path to the DOCX file.

        Returns:
            Optional[str]: Extracted text from the DOCX file if successful, None otherwise.
        """
        try:
            extracted_text: str = docx2txt.process(docx_path)
            return extracted_text
        except Exception as e:
            print(f"An error occurred: {e}")
            return None
    
    @staticmethod
    def extract_text_from_content(file_bytes):
        doc = Document(io.BytesIO(file_bytes))
        return "\n".join([p.text for p in doc.paragraphs])