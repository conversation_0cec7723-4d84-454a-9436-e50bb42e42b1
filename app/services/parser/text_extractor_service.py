"""
This module defines the TextExtractorService class, which provides a unified interface for extracting text from different types of document files. Currently, it supports extracting text from PDF and DOCX files by delegating the task to specialized extractor services based on the file extension.

The service is designed to be used asynchronously, making it suitable for applications that require non-blocking I/O operations, such as web servers.

Usage:
    The service can be used by calling the `extract_text` static method with the path to the document file. This method determines the file type based on its extension and uses the appropriate extractor service to retrieve the text.

Example:
    import asyncio
    from text_extractor_service import TextExtractorService

    async def main():
        file_path = 'path/to/your/document.pdf'  # or .docx
        text = await TextExtractorService.extract_text(file_path)
        print(text)

    asyncio.run(main())

Dependencies:
    - For PDF files: PyMuPDF
    - For DOCX files: python-docx
"""
from typing import Optional, Dict, Type
from .pdf_text_extractor import PDFTextExtractor
from .document_text_extractor import DocumentTextExtractor

class TextExtractorService:
    extractors: Dict[str, Type] = {
        '.docx': DocumentTextExtractor,
        '.pdf': PDFTextExtractor,
    }

    @staticmethod
    def is_supported_extension(filename: str) -> bool:
        """
        Checks if the file extension is supported by the extractor service.

        Args:
            filename (str): The name of the file to check.

        Returns:
            bool: True if the file extension is supported, False otherwise.
        """
        try:
            file_extension = filename[filename.rfind('.'):].lower()
            return file_extension in TextExtractorService.extractors
        except Exception as e:
            # Log the exception if needed
            print(f"Error checking file extension: {e}")
            return False

    @staticmethod
    def extract_text(file_path: str) -> Optional[str]:
        """
        Determines the file type based on its extension and calls the appropriate extractor service.
        Wraps the extraction process in a try-except block to handle and throw exceptions if extraction fails.

        Args:
            file_path (str): Path to the file from which text needs to be extracted.

        Returns:
            Optional[str]: Extracted text from the file or None if extraction failed or no text was extracted.
        """

        file_extension = file_path[file_path.rfind('.'):].lower()
        extractor_class = TextExtractorService.extractors.get(file_extension)

        if not extractor_class:
            raise ValueError("Unsupported file type.")

        try:
            return extractor_class.extract_text(file_path)
        except Exception as e:
            raise e

    @staticmethod
    def extract_text_from_content(file_bytes, extension) -> Optional[str]:
        """
        Determines the file type based on its extension and calls the appropriate extractor service.
        Wraps the extraction process in a try-except block to handle and throw exceptions if extraction fails.
        Args:
            file_bytes (bytes): The content of the file from which text needs to be extracted.
            extension (str): The file extension to determine the extractor class.
        Returns:
            Optional[str]: Extracted text from the file or None if extraction failed or no text was extracted.
        """
        

        extractor_class = TextExtractorService.extractors.get(extension)
        if not extractor_class:
            raise ValueError("Unsupported file type.")

        try:
            return extractor_class.extract_text_from_content(file_bytes)
        except Exception as e:
            raise e
