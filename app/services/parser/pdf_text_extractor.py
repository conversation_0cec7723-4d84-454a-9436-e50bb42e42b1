"""
pdf_text_extractor.py

Author: <PERSON><PERSON> <<EMAIL>>

This module provides a class, PDFTextExtractor, which offers functionality to asynchronously extract text from PDF files, including text from images within the PDFs using OCR (Optical Character Recognition). It is designed to be utilized as part of a larger application that requires asynchronous PDF text extraction capabilities.

Dependencies:
- PyMuPDF: For handling PDF files and extracting text.
- Pillow: For image processing within PDFs.
- pytesseract: For OCR capabilities to extract text from images.
- asyncio: For asynchronous programming.

Usage:
    To use this class, ensure you have the dependencies installed by running:
    `pip install PyMuPDF Pillow pytesseract`

    Then, create an instance of the PDFTextExtractor class and call the `extract_text_from_pdf` method asynchronously, passing the path to your PDF file as an argument.
    Example:
        import asyncio
        from pdf_text_extractor import PDFTextExtractor

        async def main():
            extractor = PDFTextExtractor()
            pdf_text = await extractor.extract_text('path/to/your/file.pdf')
            print(pdf_text)

        asyncio.run(main())

Public Methods:
    - async extract_text_from_pdf(pdf_path: str) -> Optional[str]: Asynchronously extracts text from the specified PDF file, including text from images using OCR. Returns the extracted text or None if extraction failed or no text was extracted.

Note: This module requires the installation of its dependencies for full functionality. Asynchronous execution requires Python 3.7 or higher.
"""

"""
pdf_text_extractor.py

Author: Zain Mustijab <<EMAIL>>

This module provides a class, PDFTextExtractor, which offers functionality to asynchronously extract text from PDF files, including text from images within the PDFs using OCR (Optical Character Recognition). It is designed to be utilized as part of a larger application that requires asynchronous PDF text extraction capabilities.

Dependencies:
- PyMuPDF: For handling PDF files and extracting text.
- Pillow: For image processing within PDFs.
- pytesseract: For OCR capabilities to extract text from images.
- asyncio: For asynchronous programming.

Usage:
    To use this class, ensure you have the dependencies installed by running:
    `pip install PyMuPDF Pillow pytesseract`

    Then, create an instance of the PDFTextExtractor class and call the `extract_text_from_pdf` method asynchronously, passing the path to your PDF file as an argument.
    Example:
        import asyncio
        from pdf_text_extractor import PDFTextExtractor

        async def main():
            extractor = PDFTextExtractor()
            pdf_text = await extractor.extract_text('path/to/your/file.pdf')
            print(pdf_text)

        asyncio.run(main())

Public Methods:
    - async extract_text_from_pdf(pdf_path: str) -> Optional[str]: Asynchronously extracts text from the specified PDF file, including text from images using OCR. Returns the extracted text or None if extraction failed or no text was extracted.

Note: This module requires the installation of its dependencies for full functionality. Asynchronous execution requires Python 3.7 or higher.
"""

import asyncio
import io
import os
from typing import Optional

import fitz  # PyMuPDF
import pytesseract
from PIL import Image, ImageOps, UnidentifiedImageError
from fastapi.concurrency import run_in_threadpool

class PDFTextExtractor:
    """
    Provides functionality to extract text from PDF files, including text from images within the PDFs using OCR.
    """

    IMAGE_SIZE_THRESHOLD = (100, 100)
    OCR_TIMEOUT_SECONDS = 30  # Define a constant for the OCR timeout duration

    @staticmethod
    def extract_text(pdf_path: str) -> Optional[str]:
        """
        Synchronously extracts text from the specified PDF file, including text from images using OCR.
        Returns the extracted text or None if extraction failed or no text was extracted.
        """
        extracted_text = ""
        try:
            with fitz.open(pdf_path) as doc:
                for page_number, page in enumerate(doc, start=1):  # Start counting pages from 1
                    page_text = PDFTextExtractor.__extract_text_from_page(page, page_number)
                    if page_text:
                        extracted_text += page_text
                    image_text = PDFTextExtractor.__extract_text_from_images(page, doc, page_number)
                    if image_text:
                        extracted_text += image_text

                if not extracted_text.strip():
                    return None

        except UnidentifiedImageError:
            print(f"Page {page_number}: Image {image_index + 1} is not a valid image or is corrupted.")
        except Exception as e:
            raise ValueError(f"Error opening or processing PDF file: {e}")

        return extracted_text if extracted_text.strip() else None

    @staticmethod
    def __extract_text_from_page(page, page_number) -> str:
        """
        Extracts text from a single page of the PDF.
        """
        page_text = ""
        try:
            page_text = page.get_text()
            if page_text:
                page_text = page_text.strip()
                print(f"Page {page_number}: Text Length - {len(page_text)}")
            else:
                print(f"Page {page_number}: No text extracted.")
        except Exception as e:
            print(f"Error extracting text from page: {e}")
        return page_text

    @staticmethod
    def __extract_text_from_images(page, doc, page_number) -> str:
        """
        Extracts text from images found on a single page of the PDF using OCR.
        """
        extracted_text = ""
        try:
            image_list = page.get_images(full=True)
            if image_list:
                for image_index, img in enumerate(image_list):
                    result = PDFTextExtractor.__process_image(img, image_index, doc, page_number)
                    extracted_text += result
            else:
                print(f"Page {page_number}: No images found.")
        except Exception as e:
            print(f"Error checking for images on page: {e}")
        return extracted_text

    @staticmethod
    def __process_image(img, image_index, doc, page_number) -> str:
        """
        Processes a single image for OCR.
        """
        extracted_text = ""
        try:
            xref = img[0]
            base_image = doc.extract_image(xref)
            if not base_image or "image" not in base_image:
                print(f"Page {page_number}: Failed to extract image {image_index + 1}")
                return ""
            image_bytes = base_image["image"]

            with Image.open(io.BytesIO(image_bytes)) as image:
                # print(f"Image Width: {image.width}, Image Height: {image.height}")
                if image.width > PDFTextExtractor.IMAGE_SIZE_THRESHOLD[0] and image.height > PDFTextExtractor.IMAGE_SIZE_THRESHOLD[1]:
                    # Convert image to grayscale
                    gray_image = ImageOps.grayscale(image)
                    ocr_extracted_text = pytesseract.image_to_string(gray_image)
                    ocr_extracted_text = ocr_extracted_text.strip()
                    extracted_text += ocr_extracted_text
                    if ocr_extracted_text:
                        print(f"Page {page_number}: Extracted text from image {image_index + 1} with length {len(ocr_extracted_text)}")
                    else:
                        print(f"Page {page_number}: Unable to extract text from image {image_index + 1}")
                else:
                    print(f"Page {page_number}: Ignored small image {image_index + 1}")
        except Exception as e:
            print(f"Error processing image {image_index + 1}: {e}")
        return extracted_text.strip()

    @staticmethod
    def extract_text_from_content(file_bytes):
        doc = fitz.open(stream=file_bytes, filetype="pdf")
        text = ""
        for page in doc:
            text += page.get_text()
        return text