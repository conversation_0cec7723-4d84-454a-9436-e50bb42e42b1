from sqlalchemy.orm import Session
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt
from datetime import datetime, timedelta
from dependencies import (
    SECRET_KEY,
    ALGORITHM,
    ACCESS_TOKEN_EXPIRE_MINUTES,
    PWD_CONTEXT,
    FORGET_PASSWORD_SECRET_KEY,
    APP_HOST,
    FORGET_PASSWORD_URL,
    USER_INVITE_URL,
    INTERVIEW_PREP_URL,
    RECRUITER_APP_HOST
)
from models.user import User
from fastapi import HTTPException, status
from custom_exceptions import AuthFailedException, EntityNotFoundException
from services.notification.email import send_email_background
from models.recruiter import Recruiter

class AuthServiceClass:
    def __authenticate_user(self, db: Session, email: str, password: str):
        try:
            user = self.__get_user_by_email(email, db)
            if not user:
                raise AuthFailedException(
                    "Invalid username or password"
                )  # "User with this email does not exist"
            if not PWD_CONTEXT.verify(password, user.password):
                raise AuthFailedException(
                    "Invalid username or password"
                )  # "Incorrect password"
            return user
        except Exception as e:
            raise e

    def create_login_access_token(self, data: dict, expires_delta: timedelta = None):
        try:
            to_encode = data.copy()
            if expires_delta:
                expire = datetime.utcnow() + expires_delta
            else:
                expire = datetime.utcnow() + timedelta(minutes=15)
            to_encode.update({"exp": expire})
            encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
            return encoded_jwt

        except jwt.PyJWTError as e:
            # Handle JWT-specific errors. Seperated for debug/unit test purpose
            raise e

        except Exception as e:
            # Handle other potential exceptions
            raise e

    def login_user(self, form_data, db):
        try:
            email = form_data.username
            user = self.__authenticate_user(db, email, form_data.password)
            access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = self.create_login_access_token(
                data={"sub": user.email}, expires_delta=access_token_expires
            )
            user.access_token = access_token
            return user
        except Exception as e:
            raise e

    def forget_password(self, email: str, background_tasks, db):
        try:
            user = self.__get_user_by_email(email, db)
            if not user:
                raise EntityNotFoundException(f"No user found with email: {email}")

            secret_token = self.__create_token(FORGET_PASSWORD_SECRET_KEY, user.email)
            reset_link = f"{APP_HOST}{FORGET_PASSWORD_URL}?resetToken={secret_token}"
            email_body = {
                "name": f"{user.first_name} {user.last_name}",
                "reset_link": reset_link,
            }
            send_email_background(
                background_tasks,
                "Reset Your Password – Vettio Account Recovery",
                user.email,
                email_body,
                "reset-password.html",
            )
            return {
                "email": user.email,
            }
        except Exception as e:
            raise e

    def reset_password(self, reset_password_request, db):
        try:
            email = self.__decode_token(
                FORGET_PASSWORD_SECRET_KEY, reset_password_request.token
            )
            user = self.__get_user_by_email(email, db)
            if not user:
                raise EntityNotFoundException(f"Invalid reset token")
            user.password = PWD_CONTEXT.hash(reset_password_request.password)
            db.commit()
            db.refresh(user)
            return user
        except JWTError as e:
            raise e

    def __get_user_by_email(self, email: str, db):
        try:
            user = db.query(User).filter(User.email == email).first()
            return user
        except Exception as e:
            raise e

    def __create_token(self, key, email: str):
        try:
            data = {
                "sub": email,
                "exp": datetime.utcnow()
                + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES),
            }
            token = jwt.encode(data, key, ALGORITHM)
            return token
        except Exception as e:
            raise e

    def __decode_token(self, key, token: str):
        try:
            payload = jwt.decode(token, key, algorithms=[ALGORITHM])
            email: str = payload.get("sub")
            return email
        except JWTError:
            return None

    # This is generalize method to create token app wide
    def create_access_token(self, email):
        try:
            data = {
                "sub": email,
            }
            token = jwt.encode(data, FORGET_PASSWORD_SECRET_KEY, ALGORITHM)
            return token
        except Exception as e:
            raise e

    def create_recruiter_access_token(self, email, db):
        try:
            recruiter = db.query(Recruiter).filter(Recruiter.email == email).first()
            if not recruiter:
                raise AuthFailedException("Invalid recruiter email")
            recruiter.token = self.create_access_token(email)
            return recruiter
        except Exception as e:
            raise e

    def __authenticate_recruiter(self, db: Session, email: str, password: str):
        try:
            user = db.query(Recruiter).filter(Recruiter.email == email).first()
            if not user:
                raise AuthFailedException(
                    "Invalid passcode"
                )  # "User with this email does not exist"
            if not PWD_CONTEXT.verify(password, user.password):
                raise AuthFailedException(
                    "Invalid passcode"
                )  # "Incorrect password"
            return user
        except Exception as e:
            print(f"exception: {e}")    
            raise e

    def login_recruiter(self, form_data, db):
        try:
            username = form_data.username
            user = self.__authenticate_recruiter(db, username, form_data.password)
            access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = self.create_login_access_token(
                data={"sub": user.email}, expires_delta=access_token_expires
            )
            user.access_token = access_token
            return user
        except Exception as e:
            raise e        

    def validate_recruiter(self, validation_data, db):
        try:
            email = self.__decode_token(
                FORGET_PASSWORD_SECRET_KEY, validation_data.token
            )
            print(f"email: {email}")
            if not email:
                raise EntityNotFoundException(f"Invalid token")
            user = db.query(Recruiter).filter(Recruiter.email == email).first()
            if not user:
                raise EntityNotFoundException(f"Something went wrong")
            access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = self.create_login_access_token(
                data={"sub": user.email}, expires_delta=access_token_expires
            )
            user.access_token = access_token
            return user
        except Exception as e:
            raise e

    def validate_user_access_token(self, access_token_request, db):
        try:
            email = self.__decode_token(
                FORGET_PASSWORD_SECRET_KEY, access_token_request.token
            )
            user = self.__get_user_by_email(email, db)
            if not user:
                raise EntityNotFoundException(f"Invalid token")
            access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = self.create_login_access_token(
                data={"sub": user.email}, expires_delta=access_token_expires
            )
            user.access_token = access_token
            return user
        except Exception as e:
            raise e

    def create_user_interview_link(self, email, db):
        try:
            user = self.__get_user_by_email(email, db)
            if not user:
                raise EntityNotFoundException(f"Invalid User")
            secret_token = self.create_access_token(user.email)
            redirect_link = f"{APP_HOST}{USER_INVITE_URL}?access_token={secret_token}"
            return redirect_link
        except Exception as e:
            raise e

    def send_interview_prepare_link(self, email, background_tasks, db):
        try:
            user = self.__get_user_by_email(email, db)
            if not user:
                raise EntityNotFoundException(f"Invalid User Email")

            secret_token = self.create_access_token(user.email)
            redirect_link = (
                f"{APP_HOST}{INTERVIEW_PREP_URL}?access_token={secret_token}"
            )

            email_body = {
                "name": f"{user.first_name} {user.last_name}",
                "redirect_link": redirect_link,
            }
            send_email_background(
                background_tasks,
                "Prepare for your interview",
                user.email,
                email_body,
                "interview-prepare.html",
            )
            return {
                "email": user.email,
            }
        except Exception as e:
            raise e

    def recruiter_forget_password(self, email: str, background_tasks, db):
        try:
            user = db.query(Recruiter).filter(Recruiter.email == email).first()
            if not user:
                raise EntityNotFoundException(f"No user found with email: {email}")

            secret_token = self.__create_token(FORGET_PASSWORD_SECRET_KEY, user.email)
            reset_link = f"{RECRUITER_APP_HOST}/{FORGET_PASSWORD_URL}?resetToken={secret_token}"
            email_body = {
                "name": f"{user.first_name} {user.last_name}",
                "reset_link": reset_link,
            }
            send_email_background(
                background_tasks,
                "Reset Your Password – Vettio Account Recovery",
                user.email,
                email_body,
                "reset-password.html",
            )
            return {
                "email": user.email,
            }
        except Exception as e:
            raise e

    def recruiter_reset_password(self, reset_password_request, db):
        try:
            email = self.__decode_token(
                FORGET_PASSWORD_SECRET_KEY, reset_password_request.token
            )
            user = db.query(Recruiter).filter(Recruiter.email == email).first()
            if not user:
                raise EntityNotFoundException(f"Invalid reset token")
            user.password = PWD_CONTEXT.hash(reset_password_request.password)
            db.commit()
            db.refresh(user)
            return user
        except JWTError as e:
            raise e
