from sqlalchemy.orm import aliased
from schemas.user import User
from helper.helper import get_user_education_object_scraping_dog, get_user_experience_object_scraping_dog
import time
from urllib.parse import urlparse
from helper.similarity import SemanticMatcher
from models.user_experience import UserExperience
from models.user import User
from sqlalchemy.orm import Session
from dependencies import SCRAPINGDOG_API_URL, SCRAPINGDOG_API_KEY
from database import get_mongodb_client, get_db
import logging
import requests
import re
from models.organization import Organization
from models.organization_detail import OrganizationDetail
from models.user import User
from urllib.parse import urlparse
from models.city import City
from services.organization.organization_detail import OrganizationDetailsServiceClass
from services.user.user import UserServiceClass

user_service_object = UserServiceClass()

org_service = OrganizationDetailsServiceClass()
logger = logging.getLogger(__name__)


db : Session = get_db()

class ScrappingDogServiceClass:
    def __init__(self):
        self.scrapping_dog_base_url = SCRAPINGDOG_API_URL
        self.scrapping_dog_api_key = SCRAPINGDOG_API_KEY
        self.mongo_client: Session = get_mongodb_client()
        self.job_collection = "web_posted_jobs"
        self.company_collection = "linkedin_companies"

    def __get_jobs_to_insert(self, jobs, profession_id, city_id):
        try:
            jobDetailData = []
            count = 0
            logger.info("Processing jobs")
            linkedin_company_detail_fetch_url = f"{self.scrapping_dog_base_url}linkedinjobs/"
            job_collection = self.mongo_client[self.job_collection]

            logger.info(f"URL: {linkedin_company_detail_fetch_url}")

            for data in jobs:
                try:
                    job_id = data.get("job_id", "")
                    logger.info(f"Processing job ID: {str(job_id)}")
                    job_exist = job_collection.count_documents({"job_id": str(job_id)})

                    if job_id != "" and job_exist <= 0 or True:
                        logger.info(f"Preparing data for job: {str(job_id)}")
                        job_details_api_response = requests.get(
                            linkedin_company_detail_fetch_url,
                            params={"api_key": self.scrapping_dog_api_key,"job_id": job_id,
                            },
                        )
                        # Get status code
                        status_code = job_details_api_response.status_code
                        # json data
                        job_details_api_response_body = job_details_api_response.json()
                        # modify leter with error handle
                        if status_code != 200:
                            logger.error(
                                f"Error fetching job details for job ID: {str(job_id)}"
                            )
                            logger.error(f"Error response: {job_details_api_response_body}")

                        job_details_data = job_details_api_response_body[0]
                        # getting company linkedin url
                        company_linkedin_url = job_details_data.get(
                            "company_linkedin_id", ""
                        )
                        if company_linkedin_url != "":
                            parsed_url = urlparse(company_linkedin_url)
                            company_linkedin_id = parsed_url.path.split("/")[-1]
                            job_details_data["company_unique_identifier"] = (
                                company_linkedin_id
                            )
                        else:
                            job_details_data["company_unique_identifier"] = ""

                        job_details_data["status"] = 1 # default status not synced with DB
                        job_details_data["profession_id"] = profession_id 
                        job_details_data["city_id"] = city_id 

                        jobDetailData.append({**jobs[count], **job_details_data})

                        count += 1
                except requests.RequestException as e:
                    logger.error(f"Error performing request to insert job function: {e}")

            return jobDetailData
        except Exception as e:
            logger.error(f"Error in __get_jobs_to_insert: {str(e)}")

    def __set_company_details(self, jobs_to_insert):
        try:
            logger.info("Processing company details")
            job_company_detail = []
            company_row_count = 0
            linkedin_companies_collection = self.mongo_client[self.company_collection]

            for job in jobs_to_insert:
                company_unique_identifier = job.get("company_unique_identifier")
                logger.info(f"Processing company ID: {str(company_unique_identifier)}")

                linkedin_company_exist = linkedin_companies_collection.count_documents(
                    {"company_unique_identifier": str(company_unique_identifier)}
                )
                # checking current job_company_detail detail array can't duplicate data
                current_company_unique_identifiers = [item["company_unique_identifier"] for item in job_company_detail]

                if company_unique_identifier != "" and linkedin_company_exist <= 0 and not company_unique_identifier in current_company_unique_identifiers:

                    linkedin_company_detail_fetch_url = f"{self.scrapping_dog_base_url}linkedin/"
                    company_detail_api_response = requests.get(
                        linkedin_company_detail_fetch_url,
                        params={
                            "api_key": self.scrapping_dog_api_key,
                            "type": "company",
                            "linkId": company_unique_identifier,
                        },
                    )
                    # Get status code
                    status_code = company_detail_api_response.status_code
                    # json data
                    company_detail_api_response_body = company_detail_api_response.json()

                    # modify leter with error handle
                    if status_code != 200:
                        logger.error("Error fetching company details")
                        logger.error(f"Error response: {company_detail_api_response_body}")

                    # update the company detail data object
                    company_detail_data = company_detail_api_response_body[0]
                    company_detail_data["company_unique_identifier"] = (
                        company_unique_identifier
                    )
                    job_company_detail.append(company_detail_data)

                company_row_count += 1

            return job_company_detail
        except Exception as e:
            logger.error(f"Error in __set_company_details: {str(e)}")

    def insert_jobs(self, input_params):
        try:
            job_collection = self.mongo_client[self.job_collection]
            linkedin_companies_collection = self.mongo_client[self.company_collection]
            # Convert query parameters to a dictionary
            query_params = dict(input_params)

            print("query_params", query_params)            
            # adding api key
            query_params["api_key"] = SCRAPINGDOG_API_KEY

            SCRAPINGDOG_API_FULL_URL = f"{self.scrapping_dog_base_url}linkedinjobs/"
            currentPage = 1
            more_record_exist = True

            all_processed_jobs = []
            while more_record_exist and currentPage <= 1:
                query_params["page"] = currentPage
                response = requests.get(SCRAPINGDOG_API_FULL_URL, params=query_params)
                # Get status code
                status_code = response.status_code
                response_body = response.json()

                # modify leter with error handle
                if status_code != 200:
                    more_record_exist == False
                    return {**response_body, "custom": "error"}

                # check the data have job to process
                if not response_body or len(response_body) == 0:
                    more_record_exist == False
                    return "no data found to process"

                # Process the jobs (assuming `response_body` contains a list of jobs)
                for job in response_body:
                    # Add job to the list (or insert into MongoDB)
                    all_processed_jobs.append(job)

                currentPage += 1

            logger.info("Jobs fetched successfully")
            logger.info(f"Total jobs fetched: {len(all_processed_jobs)}")

            jobs_to_insert = self.__get_jobs_to_insert(
                all_processed_jobs,
                input_params["profession_id"],
                input_params["city_id"],
            )
            company_details = self.__set_company_details(jobs_to_insert)

            # Insert dummy records into the collection
            if jobs_to_insert and len(jobs_to_insert) > 0:
                result = job_collection.insert_many(jobs_to_insert)
            # Insert linkedin_companies_collection
            if company_details and len(company_details) > 0:
                linkedin_companies_collection.insert_many(company_details)

            logger.info("Jobs and company details inserted successfully")
            logger.info(f"Total jobs inserted: {len(jobs_to_insert)}")
            logger.info(f"Total companies inserted: {len(company_details)}")
            return True
        except Exception as e:
            logger.error(f"Error in get_jobs: {str(e)}")

    def get_job_by_id(self, job_id):
        try:
            logger.info(f"Fetching job details for job ID: {str(job_id)}")
            query_params = {
                "api_key": self.scrapping_dog_api_key,
                "job_id": job_id,
            }
            logger.info(f"Fetching job details for job ID: {str(query_params)}")
            # request_url = "https://67d0093d823da0212a8452e5.mockapi.io/scrappingdog"
            # response = requests.get(request_url)
            request_url = f"{self.scrapping_dog_base_url}linkedinjobs"
            response = requests.get(request_url, params=query_params)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Error fetching job details for job ID: {str(job_id)}")
                logger.error(f"Error response: {response.json()}")
                return None
        except Exception as e:
            logger.error(f"Error in get_job_by_id: {str(e)}")
            return None
            
    def get_user_experiences_with_organization_name(self, user_id: int, db: Session):
        logger.info(f"Fetching experiences for user_id: {user_id}")
        
        # Join UserExperience with Organization to fetch both in one query
        experiences = (
            db.query(UserExperience, Organization.name.label("organization_name"))
            .join(Organization, UserExperience.organization_id == Organization.id, isouter=True)
            .filter(UserExperience.user_id == user_id)
            .all()
        )

        if not experiences:
            logger.warning(f"No experiences found for user_id: {user_id}")
            return []

        experiences_with_organization_name = []

        for exp, org_name in experiences:
            exp.organization_name = org_name  # Dynamically attach attribute
            experiences_with_organization_name.append(exp)

        logger.info(f"Found {len(experiences_with_organization_name)} experiences with organization names.")
        return experiences_with_organization_name

    def store_user_data_via_linkedin_scrapingdog(self, user_id: int, db: Session) -> dict:
        try:
            logger.info("Scraping linkedin data for user_id ")
            logger.info(user_id)
            
            user = self._get_user_by_id(user_id, db)
            if not user or not user.linkedin_url:
                logger.info("User not found or no Linkedin url")
                return False
            
            request = type("Request", (object,), {
                "state": type("State", (object,), {
                    "user": type("User", (object,), {"id": user_id})()
                })()
            })()

            linkedin_id = self._extract_linkedin_id(user.linkedin_url)
            if not linkedin_id:
                logger.info("No linkedin id found for the given user")
                return False

            person_data = self._fetch_linkedin_profile(linkedin_id)

            if not person_data:
                logger.error("Couldn't extract linkedin data for the user")
                return False

            person_data_experiences = person_data.get('experience', [])
            person_data_experiences_durations = [exp.get("duration", "") for exp in person_data_experiences]
            total_experience = self._calculate_total_experience(person_data_experiences_durations)
            about = person_data.get('about', None)

            user = db.query(User).filter(
                User.id == request.state.user.id).first()
            user.total_experience = total_experience
            user.about =  about
            db.commit()

            # --- Update education ----------------------------------------------------
            user_education_object = get_user_education_object_scraping_dog(
                person_data.get('education', []), db
            )

            user_service_object.update_education(user_education_object, request, db)

            # --- Process LinkedIn experiences directly -------------------------------
            linkedin_experiences = person_data.get("experience", [])

            experience_data_array = []

            for exp in linkedin_experiences:
                company_url = exp.get("company_url")
                if not company_url:
                    continue

                linkedin_org_id = self._extract_company_id(company_url)
                if not linkedin_org_id:
                    logger.info("No organization Linkedin ID found")
                    continue

                company_data = self._fetch_company_data(company_url)
                if not company_data:
                    logger.info("No company data found")
                    continue

                domain = self._extract_domain_from_url(company_data.get("website", ""))

                # Prepare data tuple
                experience_data_array.append((exp, domain, linkedin_org_id))
            user_experience_objects = get_user_experience_object_scraping_dog(
                experience_data_array, db
            )

            user_service_object.update_experience(user_experience_objects, request, db)

            return True

        except Exception as e:
            logger.error(str(e))
            raise(e)
            

    def _calculate_total_experience(self, exp_array):
        total_months = 0

        for exp in exp_array:
            years = months = 0
            year_match = re.search(r'(\d+)\s+year', exp)
            month_match = re.search(r'(\d+)\s+month', exp)

            if year_match:
                years = int(year_match.group(1))
            if month_match:
                months = int(month_match.group(1))

            total_months += years * 12 + months

        total_years = total_months // 12
        remaining_months = total_months % 12

        return f"{total_years} years {remaining_months} months"


    # def store_user_data_via_linkedin_scrapingdog(self, user_id: int, db: Session) -> dict:
    def get_linkedin_profile_by_user_id(self, user_id: int, db: Session) -> dict:
        try:
            logger.info("Scraping linkedin data for user_id ")
            logger.info(user_id)

            user = self._get_user_by_id(user_id, db)
            if not user or not user.linkedin_url:
                logger.info("User not found or no linkedin url found")
                return False

            linkedin_id = self._extract_linkedin_id(user.linkedin_url)
            if not linkedin_id:
                logger.info("Couldn't extract the Linkedin ID of the user")
                return False
           
            person_data = self._fetch_linkedin_profile(linkedin_id)
            if not person_data:
                logger.info("Couldn't fetch Linkedin data for the linkedin ID")
                return False

            linkedin_experiences = person_data.get("experience", [])
            if linkedin_experiences is None or len(linkedin_experiences) <1:
                logger.info("No linkedin experiences found for the given user")
                return False
            linkedin_company_names = [exp.get("company_name") for exp in linkedin_experiences]
            linkedin_company_urls = [exp.get("company_url") for exp in linkedin_experiences]
            name_to_url_map = dict(zip(linkedin_company_names, linkedin_company_urls))

            user_organizations = self.get_user_experiences_with_organization_name(user_id, db)
            similarity_service = SemanticMatcher()
            similarity_threshold = 0.5

            for organization in user_organizations:
                query_name = organization.organization_name
                matched_name, similarity_score = similarity_service.match_best_with_score(
                    query_name, linkedin_company_names, return_score=True
                )

                if not matched_name or similarity_score <= similarity_threshold:
                    continue

                company_url = name_to_url_map.get(matched_name)
                linkedin_org_id = self._extract_company_id(company_url)
                if not linkedin_org_id:
                    continue

                company_data = self._fetch_company_data(company_url)
                if not company_data:
                    continue

                domain = self._extract_domain_from_url(company_data.get("website", ""))
                logo = company_data.get("profile_photo", "")

                org = db.query(Organization).filter_by(linkedin_id=linkedin_org_id).first()
                if not org:
                    org = db.query(Organization).filter_by(id=organization.organization_id).first()
                    if org and not org.linkedin_id:
                        org.linkedin_id = linkedin_org_id
                        org.domain = domain
                        org.logo = logo
                        db.commit()
                        db.refresh(org)
                about = company_data.get("description") or company_data.get("about")
                if about and org:
                    org_detail = db.query(OrganizationDetail).filter_by(organization_id=org.id).first()
                    if org_detail:
                        org_detail.description = about
                    else:
                        org_detail = OrganizationDetail(
                            organization_id=org.id,
                            description=about,
                            founded=company_data.get("founded", ""),
                            industry=company_data.get("industry", ""),
                            address=company_data.get("location", ""),
                            website=company_data.get("website", ""),
                            specialities=company_data.get("specialties", ""),
                            company_size=company_data.get("company_size", ""),
                            revenue=company_data.get("revenue", "")
                        )
                        db.add(org_detail)
                    db.commit()
            logger.info("Experiences scraped and saved to postgres")
            return True

        except Exception as e:
            logger.error(str(e))
            raise(e)


    def _extract_domain_from_url(self, url: str):
        if not url:
            return None

        parsed = urlparse(url)
        return parsed.hostname.lower() if parsed.hostname else None

    def _extract_company_id(self, link: str):

        if not link:
            return None

        # normalise & isolate the path part
        path = urlparse(link).path.lower()

        # look for “…/company/<id>” where <id> = slug or numeric id
        match = re.search(r"/company/([^/?#]+)/?", path)
        res = match.group(1) if match else None
        return res
    
    def _get_user_by_id(self, user_id: int, db: Session) -> User:
        return db.query(User).filter(User.id == user_id).first()

    def _extract_linkedin_id(self, url: str) -> str:
        logger.info("LinkedIn ID of the user is ")
        match = re.search(r'linkedin\.com/in/([^/?]+)', url)
        logger.info(match.group(1) if match else None)
        return match.group(1) if match else None

    def _make_request_with_retry(self, url: str, params: dict, retries: int = 3, backoff: int = 2) -> dict:
        for attempt in range(1, retries + 1):
            try:
                response = requests.get(url, params=params)
                if response.status_code == 200:
                    data = response.json()
                    return data[0] if isinstance(data, list) and data else None
                logger.warning(f"Attempt {attempt}: Status {response.status_code}")
            except requests.RequestException as e:
                logger.warning(f"Attempt {attempt} failed with error: {e}")
            time.sleep(backoff * attempt)
        logger.error("All retry attempts failed.")
        return None
    

    def _fetch_linkedin_profile(self, linkedin_id: str) -> dict:
        params = {
            "api_key": self.scrapping_dog_api_key,
            "type": "profile",
            "linkId": linkedin_id,
            "private": "true"
        }
        url = f"{self.scrapping_dog_base_url}linkedin"
        return self._make_request_with_retry(url, params)

    def _fetch_company_data(self, company_url: str, sleep_seconds: int = 2) -> dict:
        match = re.search(r"linkedin\.com/company/([^/?#]+)", company_url)
        if not match:
            logger.warning("No company ID found in URL")
            return None

        company_id = match.group(1)
        params = {
            "api_key": self.scrapping_dog_api_key,
            "type": "company",
            "linkId": company_id,
            "private": "true"
        }
        url = f"{self.scrapping_dog_base_url}linkedin"

        time.sleep(sleep_seconds)  # 💤 Optional delay before first attempt
        return self._make_request_with_retry(url, params)
