from fastapi import HTTPException
from  models.country import Country
from sqlalchemy.exc import SQLAlchemyError
from fastapi.encoders import jsonable_encoder
import pycountry
from forex_python.converter import  CurrencyCodes
import logging 
from custom_exceptions import EntityNotFoundException

logger = logging.getLogger(__name__)


class CountryServiceClass:

    def get_countries(self, db):
        try:
            countries = db.query(Country).filter(Country.status == 1).order_by(Country.name).all()
            return countries
        except SQLAlchemyError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def add_country(self, country_name, db):
        try:
            existing_country = db.query(Country).filter(Country.name == country_name).first()
            if existing_country:
                return existing_country
            else:
                country = Country(name = country_name, status = 0)
                db.add(country)
                db.commit()
                db.refresh(country)
                return country
        except Exception as e:
            raise e

    def get_country_details(self, country_name):
        try:
            COMMON_ABBREVIATIONS = {
                "UAE": "United Arab Emirates",
                "USA": "United States",
                "UK": "United Kingdom",
                "KSA": "Saudi Arabia",
                "PRC": "China",
                "TURKEY": "Türkiye",  # Updated name for Turkey
            }
            # Normalize the country name using abbreviation mappings
            country_name = COMMON_ABBREVIATIONS.get(country_name.upper(), country_name)
            print(f"Country Name: {country_name}")

            # Attempt to find the country
            country = pycountry.countries.get(name=country_name)
            if not country:
                # Fuzzy matching if direct match fails
                country = pycountry.countries.search_fuzzy(country_name)[0]

            country_code = country.alpha_2  # Country Code
            currency = pycountry.currencies.get(numeric=country.numeric)  # Currency Code
            currency_code = currency.alpha_3 if currency else None

            # Get currency symbol
            currency_symbols = CurrencyCodes()
            currency_symbol = currency_symbols.get_symbol(currency_code) if currency_code else None
            return {
                "country_name": country_name,
                "country_code": country_code,
                "currency_code": currency_code,
                "currency_symbol": currency_symbol,
            }
        except Exception as e:
            # Log the error and return a message but continue processing
            return {
                "Country": country_name,
                "Error": f"Unable to process country '{country_name}': {str(e)}"
            }

    def add_currency(self, db):
        try:
            countries = db.query(Country).filter(Country.currency == None).all()
            res = []
            for country in countries:
                # Get currency information
                country_info = self.get_country_details(country.name)
                country.currency_symbol = country_info['currency_symbol']
                country.currency = country_info['currency_code']
                country.country_code = country_info['country_code']
                country.status = 1

                db.commit()  # Commit the changes to the existing country
                res.append(country_info)
            db.refresh(country)
            print(f"Currency details: {res}")
            return jsonable_encoder(res)
        except Exception as e:
            raise e
        
    def get_country_by_country_code(self, country_code, db):
        try:
            if country_code:
                country = (
                    db.query(Country)
                    .filter(Country.country_code == country_code)
                    .first()
                )
            return jsonable_encoder(country)
        except Exception as e:
            raise e
        
    def get_country_by_id(self, country_id, db):
        try:
            logger.info(f"Getting country by id {country_id} started")
            country = db.query(Country).filter(Country.id == country_id).first()
            if not country:
                logger.error(f"Country with id {country_id} not found")
                return ""
            logger.info(f"Getting country by id {country_id} completed")
            return country.name

        except Exception as e:
            logger.error(f"Error in getting country id: {str(e)}")
            raise e