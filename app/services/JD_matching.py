import pandas as pd
import logging
from custom_exceptions import EntityNotFoundException
import logging
from services.filemanager.filemanager import FileManagerClass
from models.requisition import Requisition
import requests
from io import BytesIO
from celery_tasks.send_interview_invite import JD_matching

logger = logging.getLogger(__name__)
file_manager_object = FileManagerClass()


class JDMatchingServiceClass:
    def __init__(self):
        pass

    def read_and_extract_variables(self, request, s3_url, db):
        try:
            logger.info(f"Read and extract variables started with s3_url: {s3_url}")
            response = requests.get(s3_url) 
            mobile_wallet_requisition_id = 4349 
            IAM_requisition_id= 4350
            response.raise_for_status()
            title="Head Engineering"
            df = pd.read_excel(BytesIO(response.content), header=1)
            logger.info(f"df had {df.head()}")
            for index, row in df.iterrows():
                email = row['Email']
                first_name = row['First Name']
                last_name = row['Last Name']
                email = row['Email']
                number = row['Number']
                resume = row['Name f Resume']
                linkdin_url = row['profile Url']
                logger.info(
                    "================================ Row Data ================================ ")
                logger.info(f"Email: {email}")

                JD_matching.delay(
                    mobile_wallet_requisition_id,
                    IAM_requisition_id,
                    title,
                    resume,
                    first_name,
                    last_name,
                    email,
                    number,
                    linkdin_url,
                )

        except Exception as e:
            logger.error(f"Error in read_and_extract_variables: {e}")
            raise e
