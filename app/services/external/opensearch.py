from models import Requisition
from services.external.openai import OpenAIServiceClass
import logging
from dependencies import (
    OPENSEARCH_REQUISITION_EMBEDDING_INDEX,
     OPENSEARCH_RC_INDEX,OPENSEARCH_CPA_INDEX,
     SEARCH_CANDIDATES_SIMILARITY_THRESHOLD
)
from models.user import User
from models.skill import Skill
from models.user_skill import UserSkill
from models.organization import Organization
from models.user_education import UserEducation
from models.user_experience import UserExperience, UserExperience
from models.school import School
from models.city import City
from models.country import Country
from models.interview_feedback import InterviewFeedback
from dependencies import OPENSEARCH_RC_INDEX

from models.interview_feedback import InterviewFeedback
import json
from datetime import datetime
import re
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
from fastapi import HTT<PERSON>Exception
from database import get_opensearch_client, close_opensearch_client
from dependencies import (
    OPENSEARCH_RESUME_INDEX,
    OPENSEARCH_REQUISITION_EMBEDDING_INDEX,
    REQ<PERSON>SITION_SIMILARITY_THRESHOLD,
    O<PERSON><PERSON><PERSON><PERSON><PERSON>_RC_INDEX,
    OPENSEARCH_RECRUITER_REQUISITION_INDEX
    # OPENSEARCH_RC_INDEX
)
import logging

logger = logging.getLogger(__name__)


class OpenSearchServiceClass:
    def __init__(self, index_name):
        self.__create_connection()
        self.index_name = index_name
        if index_name == OPENSEARCH_RESUME_INDEX:
            self.__create_index_for_resume()

        if index_name == OPENSEARCH_RC_INDEX:
            self.create_idx_for_candidate_bank()

        if index_name == OPENSEARCH_CPA_INDEX:
            self.create_idx_for_cpa()

        if index_name == OPENSEARCH_REQUISITION_EMBEDDING_INDEX:
            self.__create_index_for_requisition_embeddings()

        if index_name == OPENSEARCH_RECRUITER_REQUISITION_INDEX:
            self.create_idx_for_recruiter_requisition()

    def __create_connection(self):
        try:
            self.opensearch_client = get_opensearch_client()
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    def create_idx_for_candidate_bank(self):
        try:
            index_body = {
                "settings": {
                    "number_of_shards": 1,
                    "number_of_replicas": 0,
                    "index": {"knn": "true"}
                },
                "mappings": {
                    "properties": {
                        "person_id": {"type": "keyword"},
                        "source": {"type": "keyword"},
                        "interview_id": {"type": "text"},
                        "interview_feedback_id": {"type": "text"},
                        "name": {"type": "text"},
                        "location_preferences": {"type": "text"},
                        "notice_period": {"type": "text"}, 
                        "expected_salary": {"type": "text"},
                        "email": {"type": "keyword"},
                        "email_status": {"type": "integer"},
                        "total_experience": {"type": "integer"},
                        "skills": {"type": "keyword"},
                        "created_at": {
                        "type": "date",
                        "format": "strict_date_optional_time||epoch_millis"
                        },
                        "certifications": {
                            "type": "nested",
                            "properties": {
                                "title": {"type": "text"},
                                "company": {"type": "text"},
                                "verify_url": {"type": "keyword"},
                                "date_year": {"type": "date", "format": "yyyy"},
                                "date_month": {"type": "integer"},
                            }
                        },
                        "linkedin": {"type": "keyword"},
                        "summary": {"type": "text"},
                        "education": {
                            "type": "nested",
                            "properties": {
                                "school": {"type": "text"},
                                "degree": {"type": "text"},
                                "field": {"type": "text"},
                                "description": {"type": "text"},
                                "grade": {"type": "keyword"},
                                "start_date": {"type": "date", "format": "yyyy-MM-dd"},
                                "end_date": {"type": "date", "format": "yyyy-MM-dd"}
                            }
                        },
                        "feedback": {
                            "type": "nested",
                            "properties": {
                                "requirement": {"type": "text"},
                                "evaluation": {"type": "text"}
                            }
                        },
                        "work_experience": {
                            "type": "nested",
                            "properties": {
                                "organization": {"type": "text"},
                                "job_title": {"type": "text"},
                                "relevant_experience": {"type": "integer"},
                                "emp_type": {"type": "keyword"},
                                "location_type": {"type": "keyword"},
                                "description": {"type": "text"},
                                "about_company": {"type": "text"},
                                "start_date": {"type": "date", "format": "yyyy-MM-dd"},
                                "end_date": {"type": "date", "format": "yyyy-MM-dd"}
                            }
                        },
                        "address": {
                            "type": "object",
                            "properties": {
                                "street": {"type": "text"},
                                "city": {"type": "keyword"},
                                "state": {"type": "keyword"},
                                "zip": {"type": "keyword"},
                                "country": {"type": "keyword"}
                            }
                        },
                        "embeddings": {
                            "type": "object",
                            "properties": {
                                "skills": {
                                    "type": "knn_vector",
                                    "dimension": 3072,
                                    "method": {
                                        "space_type": "l2",
                                        "name": "hnsw",
                                        "engine": "faiss",
                                        "parameters": {}
                                    }
                                },
                                "work_experience": {
                                    "type": "knn_vector",
                                    "dimension": 3072,
                                    "method": {
                                        "space_type": "l2",
                                        "name": "hnsw",
                                        "engine": "faiss",
                                        "parameters": {}
                                    }
                                },
                                "feedback": {
                                    "type": "knn_vector",
                                    "dimension": 3072,
                                    "method": {
                                        "space_type": "l2",
                                        "name": "hnsw",
                                        "engine": "faiss",
                                        "parameters": {}
                                    }
                                },
                                "education": {
                                    "type": "knn_vector",
                                    "dimension": 3072,
                                    "method": {
                                        "space_type": "l2",
                                        "name": "hnsw",
                                        "engine": "faiss",
                                        "parameters": {}
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if not self.opensearch_client.indices.exists(index=OPENSEARCH_RC_INDEX):
                self.opensearch_client.indices.create(index=OPENSEARCH_RC_INDEX, body=index_body)
                logger.info(f"Index '{OPENSEARCH_RC_INDEX}' created.")
            else:
                logger.info(f"Index '{OPENSEARCH_RC_INDEX}' already exists.")
        except Exception as e:
            logger.error(f"Error creating index for requisition embeddings: {e}")
            pass

    def create_idx_for_recruiter_requisition(self):
        try:
            index_body = {
    "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 0
    },
    "mappings": {
        "properties": {
            "job_title": {
                "type": "text"
            },
            "status": {
                "type": "integer"
            },
            "recruiter_contact":{
                "type":"text"
            },
            "recruiter_first_name": {
                "type": "text"
            },
            "recruiter_last_name": {
                "type": "text"
            },
            "recruiter_email_address": {
                "type": "keyword"
            },
            "recruiter_company_name": {
                "type": "text"
            },
            "hash": {
                "type": "keyword"
            },
            "must_have_technical_skills": {
                "type": "text"
            },
            "company_description": {
                 "type": "text"
            },
            "preferred_previous_positions": {
                "type": "text"
            },
            "preferred_industries": {
                "type": "text"
            },
            "required_experience_years": {
                "type": "text"
            },
            "specific_tools_technologies_certifications": {
                "type": "text"
            },
            "location_requirements": {
                "type": "text"
            },
            "onsite_or_hybrid_details": {
                "type": "nested",
                "properties": {
                    "office_location": {
                        "type": "text"
                    },
                    "relocation_openness": {
                        "type": "text"
                    },
                    "international_candidate_openness": {
                        "type": "text"
                    }
                }
            },
            "fully_remote_details": {
                "type": "nested",
                "properties": {
                    "remote_restrictions": {
                        "type": "text"
                    }
                }
            },
            "organizational_context": {
                "type": "nested",
                "properties": {
                    "reporting_position": {
                        "type": "text"
                    },
                    "has_direct_reports": {
                        "type": "text"
                    },
                    "direct_reports_count": {
                        "type": "text"
                    },
                    "team_size": {
                        "type": "text"
                    }
                }
            },
            "negative_constraints": {
                "type": "text"
            },
            "role_pitch": {
                "type": "text"
            },
            "salary_details": {
                "type": "nested",
                "properties":{
                    "salary_range":{
                        "type": "text"
                    },
                    "show_salary":{
                        "type": "bool"
                    },
                    "currency":{
                        "type": "keyword"
                    }
                }
            },
            "transcript":{
                "type":"nested",
                "properties":{
                    "role":{
                        "type":"text"
                    },
                    "content":{
                        "type":"text"
                    }
                }
            }
        }
    }
}
            if not self.opensearch_client.indices.exists(index=OPENSEARCH_RECRUITER_REQUISITION_INDEX):
                self.opensearch_client.indices.create(index=OPENSEARCH_RECRUITER_REQUISITION_INDEX, body=index_body)
                logger.info(f"Index '{OPENSEARCH_RECRUITER_REQUISITION_INDEX}' created.")
            else:
                logger.info(f"Index '{OPENSEARCH_RECRUITER_REQUISITION_INDEX}' already exists.")
        except Exception as e:
            logger.error(f"Error creating index for requisition embeddings: {e}")
            pass

    def create_idx_for_cpa(self):
        try:
            index_body = {
                "settings": {
                    "number_of_shards": 1,
                    "number_of_replicas": 0,
                    "index": {"knn": "true"}
                },
                "mappings": {
                    "properties": {
                        "person_id": {"type": "keyword"},
                        "source": {"type": "keyword"},
                        "name": {"type": "text"},
                        "email": {"type": "keyword"},
                        "total_experience": {"type": "integer"},
                        "skills": {"type": "keyword"},
                        "linkedin": {"type": "keyword"},
                        "summary": {"type": "text"},
                        "education": {
                            "type": "nested",
                            "properties": {
                                "school": {"type": "text"},
                                "degree": {"type": "text"},
                                "field": {"type": "text"},
                                "description": {"type": "text"},
                                "grade": {"type": "keyword"},
                                "city": {"type": "keyword"},
                                "country": {"type": "keyword"},
                                "start_date": {"type": "date", "format": "yyyy-MM-dd"},
                                "end_date": {"type": "date", "format": "yyyy-MM-dd"}
                            }
                        },
                        "work_experience": {
                            "type": "nested",
                            "properties": {
                                "organization": {"type": "text"},
                                "job_title": {"type": "text"},
                                "relevant_experience": {"type":"integer"},
                                "emp_type": {"type":"keyword"},
                                "location_type": {
                                    "type": "keyword",
                                },
                                "description": {
                                    "type": "text",
                                },
                                "start_date": {
                                    "type": "date",
                                    "format": "yyyy-MM-dd",
                                },
                                "end_date": {
                                    "type": "date",
                                    "format": "yyyy-MM-dd",
                                }
                            }
                        },
                        "address": {
                            "type": "object",
                            "properties": {
                                "street": {"type": "text"},
                                "city": {"type": "keyword"},
                                "state": {"type": "keyword"},
                                "zip": {"type": "keyword"},
                                "country": {"type": "keyword"}
                            }
                        },
                         "embeddings": {
                            "type": "object",
                            "properties": {
                                "skills": {
                                    "type": "knn_vector",
                                    "dimension": 3072,
                                    "method": {
                                        "space_type": "l2",
                                        "name": "hnsw",
                                        "engine": "faiss",
                                        "parameters": {}
                                    }
                                },
                                "work_experience": {
                                    "type": "knn_vector",
                                    "dimension": 3072,
                                    "method": {
                                        "space_type": "l2",
                                        "name": "hnsw",
                                        "engine": "faiss",
                                        "parameters": {}
                                    }
                                },
                                "education": {
                                    "type": "knn_vector",
                                    "dimension": 3072,
                                    "method": {
                                        "space_type": "l2",
                                        "name": "hnsw",
                                        "engine": "faiss",
                                        "parameters": {}
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if not self.opensearch_client.indices.exists(index=OPENSEARCH_CPA_INDEX):
                self.opensearch_client.indices.create(index=OPENSEARCH_CPA_INDEX, body=index_body)
                logger.info(f"Index '{OPENSEARCH_CPA_INDEX}' created.")
            else:
                logger.info(f"Index '{OPENSEARCH_CPA_INDEX}' already exists.")

        except Exception as e:
            logger.error(f"Error creating index for requisition schema: {e}")
            pass



    def __create_index_for_resume(self):
        try:
            index_body = {
                "mappings": {
                    "properties": {
                        "name": {"type": "text"},
                        "email": {"type": "keyword"},
                        "total_experience": {"type": "text"},
                        "skills": {"type": "keyword"},
                        "education": {"type": "text"},
                        "certifications": {"type": "keyword"},
                        "projects": {
                            "type": "nested",
                            "properties": {
                                "name": {"type": "text"},
                                "description": {"type": "text"},
                                "duration": {"type": "text"},
                            },
                        },
                        "work_experience": {
                            "type": "nested",
                            "properties": {
                                "company": {"type": "text"},
                                "position": {"type": "text"},
                                "duration": {"type": "text"},
                                "description": {"type": "text"},
                            },
                        },
                        "linkedin": {"type": "keyword"},
                        "number": {"type": "keyword"},
                        "address": {"type": "text"},
                        "summary": {"type": "text"},
                    }
                }
            }

            response = self.opensearch_client.indices.create(
                self.index_name, body=index_body
            )
            return response
        except Exception as e:
            pass

    def insert_to_opensearch_auto_id(self, data):
        try:
            if not data:
                return None

            response = self.opensearch_client.index(
                index=self.index_name, body=data, refresh=True
            )
            return response
        except Exception as e:
            logger.error(f"Error inserting data into OpenSearch: {e}")
            return None
        finally:
            close_opensearch_client(self.opensearch_client)

    def update_work_experience_in_opensearch(self, person_id, work_experience_payload):
        logger.info("Updating work experience in opensearch for existing user...")
        """
        Update the `work_experience` field in OpenSearch for a given person_id.

        Parameters:
        - person_id (int): The ID of the person whose work experience needs updating.
        - work_experience_payload (list): The new work experience list (dicts).
        """
        try:
            # Step 1: Search for the document by person_id
            response = self.opensearch_client.search(
                index=OPENSEARCH_RC_INDEX,
                body={
                    "query": {
                        "term": {
                            "person_id": person_id
                        }
                    }
                }
            )

            hits = response.get("hits", {}).get("hits", [])
            if not hits:
                logger.warning(f"No document found with person_id={person_id}")
                return

            doc_id = hits[0]["_id"]

            # Step 2: Update the work_experience field
            update_response = self.opensearch_client.update(
                index=OPENSEARCH_RC_INDEX,
                id=doc_id,
                body={
                    "doc": {
                        "work_experience": work_experience_payload
                    }
                }
            )

            logger.info(f"Updated work_experience for person_id={person_id}")
            return update_response

        except Exception as e:
            logger.error(f"Error updating work_experience for person_id={person_id}: {e}")

        finally:
            close_opensearch_client(self.opensearch_client)







    def update_address_in_opensearch(self, person_id, update_payload):
        try:
            # Step 1: Search by person_id to get the document _id and current data
            response = self.opensearch_client.search(
                index=OPENSEARCH_RC_INDEX,
                body={
                    "query": {
                        "term": {
                            "person_id": person_id
                        }
                    }
                }
            )

            hits = response.get("hits", {}).get("hits", [])
            if not hits:
                logger.warning(f"No document found with person_id={person_id}")
                return

            doc_id = hits[0]["_id"]
            existing_doc = hits[0].get("_source", {})

            # Step 2: If address already exists, don't update it
            if "address" in existing_doc and "address" in update_payload:
                logger.info(f"Address already exists for person_id={person_id}, skipping address update.")
                update_payload.pop("address")

            # Step 3: If nothing to update after pop, return
            if not update_payload:
                logger.info(f"No new fields to update for person_id={person_id}")
                return

            # Step 4: Perform the update
            update_response = self.opensearch_client.update(
                index=OPENSEARCH_RC_INDEX,
                id=doc_id,
                body={
                    "doc": update_payload
                }
            )

            logger.info(f"Updated enrichment status for person_id={person_id}")
            return update_response

        except Exception as e:
            logger.error(f"Error updating person_id={person_id}: {e}")

        finally:
            close_opensearch_client(self.opensearch_client)



    def insert_into_opensearch(self, document_id, data):
        try:
            if not data:
                return None

            response = self.opensearch_client.index(
                index=self.index_name, id=document_id, body=data, refresh=True
            )
            return response
        except Exception as e:
            logger.error(f"Error inserting data into OpenSearch: {e}")
            return None
        finally:
            close_opensearch_client(self.opensearch_client)

    def get_resume_from_opensearch(self, user_id):
        try:
            response = self.opensearch_client.get(index=self.index_name, id=user_id)
            resume_data = response["_source"]
            transformed_data = self.transform_resume_data(resume_data)
            return transformed_data
        except Exception as e:
            raise HTTPException(status_code=500, detail="Resume not found")

    def transform_resume_data(self, resume_data):
        return resume_data

    def __create_index_for_requisition_embeddings(self):
        try:
            index_body = {
                "settings": {
                    "number_of_shards": 1,
                    "number_of_replicas": 0,
                    "index": {"knn": "true"},
                },
                "mappings": {
                    "properties": {
                        "id": {"type": "keyword"},
                        "embeddings": {
                            "type": "knn_vector",
                            "dimension": 3072,
                            "method": {
                                "space_type": "l2",
                                "name": "hnsw",
                                "engine": "faiss",
                                "parameters": {},
                            },
                        },
                        "text": {"type": "text"},
                        "title": {"type": "text"},
                        "document_id": {"type": "keyword"},
                        "cost": {"type": "float"},
                    }
                },
            }
            if not self.opensearch_client.indices.exists(
                index=OPENSEARCH_REQUISITION_EMBEDDING_INDEX
            ):
                self.opensearch_client.indices.create(
                    index=OPENSEARCH_REQUISITION_EMBEDDING_INDEX, body=index_body
                )
                logger.info(
                    f"Index '{OPENSEARCH_REQUISITION_EMBEDDING_INDEX}' created."
                )
            else:
                logger.info(
                    f"Index '{OPENSEARCH_REQUISITION_EMBEDDING_INDEX}' already exists."
                )
        except Exception as e:
            logger.error(f"Error creating index for requisition embeddings: {e}")
            pass

    def get_single_document(self, document_id):
        try:
            response = self.opensearch_client.get(index=self.index_name, id=document_id)
            return response["_source"]
        except Exception as e:
            logger.error(f"Error getting document from OpenSearch: {e}")
            return None

    def get_similar_documents(
        self,
        query_vector: list,
        top_k: int = 5,
        min_similarity: float = REQUISITION_SIMILARITY_THRESHOLD,
    ):
        query = {
            "size": top_k,
            "query": {
                "script_score": {
                    "query": {
                        "match_all": {}
                    },
                    "script": {
                        "source": "knn_score",
                        "lang": "knn",
                        "params": {
                            "field": "embeddings",
                            "query_value": query_vector,
                            "space_type": "l2"  
                        }
                    }
                }
            }
        }
        try:
            logger.info(f"Similary Threshold: {min_similarity}")
            response = self.opensearch_client.search(index=self.index_name, body=query)
            similar_results = []
            for hit in response["hits"]["hits"]:
                similarity_score = hit["_score"]
                if similarity_score >= min_similarity:
                    similar_results.append(
                        {
                            "document_id": hit["_id"],
                            "title": hit["_source"].get("title"),
                            "similarity": similarity_score,
                        }
                    )
                else:
                    logger.info(f"Similarity score below threshold: {similarity_score} Title: {hit['_source'].get('title')}")

            return similar_results
        except Exception as e:
            logger.error(f"Error retrieving similar vectors: {str(e)}")
            return str(e)
        finally:
            close_opensearch_client(self.opensearch_client)

    def check_id_exists(self, user_id):
        logger.info("Checking if user exists in opensearch...")
        query = {
            "query": {
                "term": {
                    "person_id": str(user_id)
                }
            }
        }

        response = self.opensearch_client.search(index=self.index_name, body=query)
        return response["hits"]["total"]["value"] > 0
        
    def get_candidate_data_from_opensearch(self, person_id):
        logger.info(f"Fetching candidate data for person_id: {person_id} from OpenSearch...")
        
        query = {
            "query": {
                "term": {
                    "person_id": str(person_id)
                }
            }
        }

        try:
            response = self.opensearch_client.search(index=self.index_name, body=query)
            hits = response.get("hits", {}).get("hits", [])

            if not hits:
                logger.warning(f"No candidate found in OpenSearch for person_id: {person_id}")
                return None

            full_data = hits[0]["_source"]

            # Fields to exclude
            excluded_fields = {
                "source", "interview_id", "interview_feedback_id", "created_at",
                "name", "email", "email_status", "embeddings", "linkedin"
            }

            # Remove excluded fields
            filtered_data = {k: v for k, v in full_data.items() if k not in excluded_fields}

            logger.info(f"Candidate data (filtered) retrieved successfully for person_id: {person_id}")
            return filtered_data

        except Exception as e:
            logger.error(f"Error retrieving candidate data for person_id: {person_id}: {e}")
            return None

    def check_enriched_status(self, user_id):
        logger.info("Checking user's company info enriched status in opensearch...")
        # Step 1: Search for the document by user_id
        query = {
            "query": {
                "term": {
                    "person_id": str(user_id)
                }
            }
        }

        response = self.opensearch_client.search(index=self.index_name, body=query)
        hits = response.get("hits", {}).get("hits", [])

        if not hits:
            # No document found
            return False

        doc = hits[0]
        doc_id = doc["_id"]
        source = doc["_source"]

        # Step 2: Check if 'enriched_status' exists
        if "enriched_status" in source:
            return source["enriched_status"]
        else:
            # Step 3: Update document to add 'enriched_status': False
            update_body = {
                "doc": {
                    "enriched_status": False
                }
            }
            self.opensearch_client.update(index=self.index_name, id=doc_id, body=update_body)
            return False

    def set_enriched_status_true(self, user_id):
        logger.info("Setting enriched status to True")
        # Step 1: Search for the document by user_id
        query = {
            "query": {
                "term": {
                    "person_id": str(user_id)
                }
            }
        }

        response = self.opensearch_client.search(index=self.index_name, body=query)
        hits = response.get("hits", {}).get("hits", [])

        if not hits:
            # No document found
            return False

        doc = hits[0]
        doc_id = doc["_id"]

        # Step 2: Update the enriched_status to True
        update_body = {
            "doc": {
                "enriched_status": True
            }
        }

        self.opensearch_client.update(index=self.index_name, id=doc_id, body=update_body)
        logger.info("Enriched status set to True")
        return True


    def get_similar_candidates(self, query_vector: list, exp_greater_than_or_equals, exp_less_than_or_equals, total_exp: int = 3, city: str = "Karachi", top_k: int = 50):
        queries = {
            "skills": "embeddings.skills",
            "work_exp": "embeddings.work_experience",
            "feedback": "embeddings.feedback",
        }

        search_responses = {}

        try:
            for key, field in queries.items():
                # Construct the range query dynamically
                experience_range = {"gte": float(exp_greater_than_or_equals)}
                if exp_less_than_or_equals is not None:
                    experience_range["lte"] = float(exp_less_than_or_equals)

                filters = [
                    {
                        "range": {
                            "total_experience": experience_range
                        }
                    }
                ]

                logger.info(filters)
                # You can uncomment the city filter if you want to add it later
                # filters.insert(0, {"term": {"address.city": city}})

                search_query = {
                    "size": top_k,
                    "query": {
                        "script_score": {
                            "query": {
                                "bool": {
                                    "filter": filters
                                }
                            },
                            "script": {
                                "source": "knn_score",
                                "lang": "knn",
                                "params": {
                                    "field": field,
                                    "query_value": query_vector,
                                    "space_type": "l2",
                                },
                            },
                        }
                    }
                }
                search_responses[key] = self.opensearch_client.search(
                    index=self.index_name, body=search_query
                )

            similar_results = {}

            for key, response in search_responses.items():
                weight = 0.60 if key == "work_exp" else 0.20
                for hit in response["hits"]["hits"]:
                    document_id = hit["_id"]
                    similarity_score = hit["_score"]
                    source_data = hit.get("_source", {})
                    source_data.pop("embeddings", None)

                    if document_id not in similar_results:
                        similar_results[document_id] = {
                            "skills_similarity": 0.0,
                            "work_exp_similarity": 0.0,
                            "feedback_similarity": 0.0,
                            "total_score": 0.0,
                            "candidate_data": source_data,

                        }

                    similarity_field = f"{key}_similarity"
                    similar_results[document_id][similarity_field] = similarity_score
                    similar_results[document_id]["total_score"] += weight * \
                        similarity_score

            return similar_results
        except Exception as e:
            logger.error(f"Error retrieving similar vectors: {str(e)}")
            return str(e)
        finally:
            close_opensearch_client(self.opensearch_client)

    def _normalize_location_preference(self, pref: str) -> list:
        """Normalize location preference to handle variations like On-site/Onsite."""
        pref = pref.strip()
        variations = [pref]
        
        # Handle On-site/Onsite variations
        if "onsite" in pref.lower():
            if "-" in pref:
                # If it has hyphen, add version without hyphen
                variations.append(pref.replace("-", ""))
            else:
                # If no hyphen, add version with hyphen
                variations.append(pref.replace("onsite", "on-site", 1))
                variations.append(pref.replace("Onsite", "On-site", 1))
        
        return variations

    def _build_location_preference_filters(self, filter_data: dict) -> list:
        """Build location preference filters with normalization for variations."""
        must_clauses = []
        
        # Single location preference
        if location_pref := filter_data.get("location_preferences"):
            variations = self._normalize_location_preference(location_pref)
            should_clauses = []
            for variation in variations:
                should_clauses.append({
                    "wildcard": {
                        "location_preferences": {
                            "value": f"*{variation}*",
                            "case_insensitive": True
                        }
                    }
                })
            
            must_clauses.append({
                "bool": {
                    "should": should_clauses,
                    "minimum_should_match": 1
                }
            })
        
        # Multiple location preferences (OR logic)
        if location_prefs := filter_data.get("location_preferences"):
            if isinstance(location_prefs, str):
                location_prefs = [pref.strip() for pref in location_prefs.split(',')]
            
            should_clauses = []
            for pref in location_prefs:
                variations = self._normalize_location_preference(pref)
                for variation in variations:
                    should_clauses.append({
                        "wildcard": {
                            "location_preferences": {
                                "value": f"*{variation}*",
                                "case_insensitive": True
                            }
                        }
                    })
            
            if should_clauses:
                must_clauses.append({
                    "bool": {
                        "should": should_clauses,
                        "minimum_should_match": 1
                    }
                })
        
        # Specific location preference matching (for exact matches)
        if required_location_prefs := filter_data.get("required_location_preferences"):
            if isinstance(required_location_prefs, str):
                required_location_prefs = [pref.strip() for pref in required_location_prefs.split(',')]
            
            for pref in required_location_prefs:
                variations = self._normalize_location_preference(pref)
                should_clauses = []
                for variation in variations:
                    should_clauses.append({
                        "wildcard": {
                            "location_preferences": {
                                "value": f"*{variation}*",
                                "case_insensitive": True
                            }
                        }
                    })
                
                must_clauses.append({
                    "bool": {
                        "should": should_clauses,
                        "minimum_should_match": 1
                    }
                })
        
        return must_clauses
    
    def _build_location_filters(self, filter_data: dict) -> list:
        """Build location-based must clauses with ID priority over names (names can be list)."""
        must_clauses = []

        # City filtering - prioritize ID over name(s)
        city_id = filter_data.get("city_id", 0)
        city_names = filter_data.get("city", [])
        if city_id > 0:
            must_clauses.append({"term": {"address.city_id": city_id}})
        elif city_names:
            if isinstance(city_names, str):
                city_names = [city_names]
            city_shoulds = [
                {
                    "wildcard": {
                        "address.city": {
                            "value": name.lower(),
                            "case_insensitive": True
                        }
                    }
                } for name in city_names if name
            ]
            if city_shoulds:
                must_clauses.append({
                    "bool": {
                        "should": city_shoulds,
                        "minimum_should_match": 1
                    }
                })

        # State filtering - prioritize ID over name(s)
        state_id = filter_data.get("state_id", 0)
        state_names = filter_data.get("state", [])
        if state_id > 0:
            must_clauses.append({"term": {"address.state_id": state_id}})
        elif state_names:
            if isinstance(state_names, str):
                state_names = [state_names]
            state_shoulds = [
                {
                    "wildcard": {
                        "address.state": {
                            "value": name.lower(),
                            "case_insensitive": True
                        }
                    }
                } for name in state_names if name
            ]
            if state_shoulds:
                must_clauses.append({
                    "bool": {
                        "should": state_shoulds,
                        "minimum_should_match": 1
                    }
                })

        # Country filtering - prioritize ID over name(s)
        country_id = filter_data.get("country_id", 0)
        country_names = filter_data.get("country", [])
        if country_id > 0:
            must_clauses.append({"term": {"address.country_id": country_id}})
        elif country_names:
            if isinstance(country_names, str):
                country_names = [country_names]
            country_shoulds = [
                {
                    "wildcard": {
                        "address.country": {
                            "value": name.lower(),
                            "case_insensitive": True
                        }
                    }
                } for name in country_names if name
            ]
            if country_shoulds:
                must_clauses.append({
                    "bool": {
                        "should": country_shoulds,
                        "minimum_should_match": 1
                    }
                })

        return must_clauses

    def _build_experience_filters(self, filter_data: dict) -> list:
        """Build experience-based range filters."""
        range_filters = []
        
        exp_gte = filter_data.get("exp_greater_than_or_equals")
        exp_lte = filter_data.get("exp_less_than_or_equals")

        if exp_gte is not None:
            exp_gte = int(exp_gte)
        if exp_lte is not None:
            exp_lte = int(exp_lte)

        if exp_gte is not None and exp_lte is not None:
            if exp_gte == exp_lte:
                # Exact match: create a ~1 year band around the value
                range_filters.append({
                    "range": {
                        "total_experience": {
                            "gte": max(0, exp_gte - 1),  # avoid negative experience
                            "lte": exp_lte + 1
                        }
                    }
                })
            else:
                # Use the provided range directly
                range_filters.append({
                    "range": {
                        "total_experience": {
                            "gte": exp_gte,
                            "lte": exp_lte
                        }
                    }
                })

        elif exp_gte is not None:
            range_filters.append({
                "range": {
                    "total_experience": {
                        "gte": exp_gte
                    }
                }
            })

        elif exp_lte is not None:
            range_filters.append({
                "range": {
                    "total_experience": {
                        "lte": exp_lte
                    }
                }
            })

        logger.info(f"Experience range filters: {range_filters}")
        return range_filters


    def _build_organization_filter(self, filter_data: dict) -> list:
        """Build organization-based nested filters."""
        must_clauses = []
        if organization := filter_data.get("organization"):
            must_clauses.append({
                "nested": {
                    "path": "work_experience",
                    "query": {
                        "bool": {
                            "must": [
                                {
                                    "wildcard": {
                                        "work_experience.organization": {
                                            "value": organization.lower(),
                                            "case_insensitive": True
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            })
        return must_clauses

    def _build_knn_functions(self, query_vector: list, mode: str) -> list:
        """Build k-NN scoring functions based on search mode."""
        functions = []
        if mode in ("both", "skills"):
            functions.append({
                "script_score": {
                    "script": {
                        "source": "knn_score",
                        "lang": "knn",
                        "params": {
                            "field": "embeddings.skills",
                            "query_value": query_vector,
                            "space_type": "l2"
                        }
                    }
                },
                "weight": 0.5 if mode == "both" else 1.0
            })
        if mode in ("both", "exp"):
            functions.append({
                "script_score": {
                    "script": {
                        "source": "knn_score",
                        "lang": "knn",
                        "params": {
                            "field": "embeddings.work_experience",
                            "query_value": query_vector,
                            "space_type": "l2"
                        }
                    }
                },
                "weight": 0.5 if mode == "both" else 1.0
            })
        return functions

    def _build_search_body(self, must_clauses: list, range_filters: list, functions: list, 
                        page: int, page_size: int, min_score: float, most_recent: bool,
                        user_query: str = None) -> dict:
        """Build the complete OpenSearch request body with optional highlighting."""
        from_offset = max((page - 1) * page_size, 0)

        # Base query
        bool_query = {
            "must": must_clauses,
            "filter": range_filters
        }

        # # Add user_query for highlighting only (non-scoring)
        # if user_query:
        #     bool_query["should"] = [
        #         {
        #             "simple_query_string": {
        #                 "query": user_query,
        #                 "fields": ["work_experience.description"],
        #                 "default_operator": "and",
        #                 "flags": "OR|AND|PHRASE|PREFIX",  # optional, allows operators
        #                 "analyze_wildcard": False
        #             }
        #         }
        #     ]
        #     bool_query["minimum_should_match"] = 0


        search_body = {
            "from": from_offset,
            "size": page_size,
            "min_score": min_score,
            "explain": True,
            "query": {
                "function_score": {
                    "query": {
                        "bool": bool_query
                        
                    },
                    "functions": functions,
                    "score_mode": "sum",
                    "boost_mode": "replace"
                }
            },
            "track_scores": True,
            # "highlight": {
            #     "fields": {
            #         "work_experience.description": {
            #             "fragment_size":500,
            #             "number_of_fragments": 2,
            #             "pre_tags": ["<mark>"],
            #             "post_tags": ["</mark>"]
            #         }
            #     }
            # },

        }

        # Add sorting logic
        if most_recent:
            search_body["sort"] = [
                {"_score": {"order": "desc"}},
                {"person_id": {"order": "desc"}}
            ]
        else:
            search_body["sort"] = [
                {"_score": {"order": "desc"}}
            ]

        return search_body
    
    def _extract_function_scores(self, explanation):
        """Recursively extract scores from explain output without nesting."""

        skills_score = 0
        work_exp_score = 0

        stack = explanation.get("details", []) if explanation else []

        while stack:
            item = stack.pop()
            desc = item.get("description", "")
            value = item.get("value", 0)

            if "embeddings.skills" in desc:
                skills_score = value
            elif "embeddings.work_experience" in desc:
                work_exp_score = value

            if "details" in item:
                stack.extend(item["details"])

        return skills_score, work_exp_score


    def _format_search_results(self, response: dict, page: int, page_size: int) -> dict:
        hits = response["hits"]["hits"]
        items = {}

        for hit in hits:
            doc_id = hit["_id"]
            src = hit["_source"]
            src.pop("embeddings", None)

            explanation = hit.get("_explanation", {})
            skills_score, work_exp_score = self._extract_function_scores(explanation)

            logger.info(f"Candidate {doc_id} - Skills Score: {skills_score:.4f}, Work Exp Score: {work_exp_score:.4f}")

            items[doc_id] = {
                "total_score": hit.get("_score"),
                "skills_score": skills_score,
                "work_exp_score": work_exp_score,
                "candidate_data": src,
            }

        total_hits = response["hits"]["total"]["value"]
        last_page = (total_hits + page_size - 1) // page_size

        return {
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total_hits,
                "last_page": last_page
            },
            "items": items
        }


    def get_search_candidates(
        self,
        query_vector: list,
        mode: str = "both",
        filter_data: dict = None,
        page: int = 1,
        page_size: int = 10,
        min_score: float = SEARCH_CANDIDATES_SIMILARITY_THRESHOLD,
        most_recent: bool = False,
        user_query: str=None
    ):
        """
        Perform a hybrid vector search in OpenSearch using function_score with
        combined k-NN script_score functions, server-side filters, pagination,
        sorting, and a min_score threshold.
        """
        logger.info("Starting candidate search...")
        logger.info(filter_data)
        
        # Default filter_data
        filter_data = filter_data or {}
        
        # Build all filter components
        must_clauses = []
        must_clauses.extend(self._build_location_filters(filter_data))
        must_clauses.extend(self._build_organization_filter(filter_data))
        must_clauses.extend(self._build_location_preference_filters(filter_data))
        
        range_filters = self._build_experience_filters(filter_data)
        functions = self._build_knn_functions(query_vector, mode)
        
        # Build search body
        search_body = self._build_search_body(
            must_clauses, range_filters, functions, 
            page, page_size, min_score, most_recent, user_query
        )
        
        logger.info(f"Sorting by person_id {'desc' if most_recent else 'asc'} with score priority.")
        
        try:
            response = self.opensearch_client.search(
                index=self.index_name,
                body=search_body
            )
            return self._format_search_results(response, page, page_size)
            
        except Exception as e:
            logger.error(f"Error retrieving similar vectors: {e}")
            raise
        finally:
            close_opensearch_client(self.opensearch_client)

    def get_similar_cand_JD_matching(self,email, query_vector: list, top_k: int = 1):
        queries = {
            "skills": "embeddings.skills",
            "work_exp": "embeddings.work_experience",
        }

        search_responses = {}

        try:
            for key, field in queries.items():

                search_query = {
                    "query": {
                        "script_score": {
                            "query": {
                                "bool": {
                                    "filter":  {
                                            "term": {
                                                "email": email
                                            }
                                        }
                                }
                            },
                            "script": {
                                "source": "knn_score",
                                "lang": "knn",
                                "params": {
                                    "field": field,
                                    "query_value": query_vector,
                                    "space_type": "l2",
                                },
                            },
                        }
                    }
                }
                search_responses[key] = self.opensearch_client.search(
                    index=self.index_name, body=search_query
                )

            total_score=0
            for key, response in search_responses.items():
                weight = 0.60 if key == "work_exp" else 0.40
                logger.info(f"ffffffffffffffffffffffffff{response}")
                for hit in response["hits"]["hits"]:
                    similarity_score = hit["_score"]
                    total_score += weight * similarity_score
            logger.info(f"TOTALLLLLLL SCOREEE{total_score}")
            return total_score
        except Exception as e:
            logger.error(f"Error retrieving similar vectors: {str(e)}")
            return str(e)
        finally:
            close_opensearch_client(self.opensearch_client)

    def update_by_user_id(self, user_id, update_data):
        try:
            script = {
                "script": {
                    "lang": "painless",
                    "source": """
                        if (ctx._source.embeddings == null) {
                            ctx._source.embeddings = [:];
                        }
                        ctx._source.interview_id = params.interview_id;
                        ctx._source.interview_feedback_id = params.interview_feedback_id;
                        ctx._source.feedback = params.feedback;
                        ctx._source.skills = params.skills;
                        ctx._source.embeddings.feedback = params.feedback_embedding;
                    """,
                    "params": {
                        "interview_id": update_data["doc"].get("interview_id"),
                        "interview_feedback_id": update_data["doc"].get("interview_feedback_id"),
                        "feedback": update_data["doc"].get("feedback"),
                        "skills": update_data["doc"].get("skills"),
                        "feedback_embedding": update_data["doc"].get("embeddings.feedback")
                    }
                },
                "query": {
                    "term": {
                        "person_id": user_id
                    }
                }
            }
            return self.opensearch_client.update_by_query(index=self.index_name, body=script)
        except Exception as e:
            logger.error(f"Error updating user_id {user_id}: {e}")
            return None

    def update_work_experience_embedding_by_user_id(self, user_id, update_data):
        logger.info("Updating work experience embedding in opensearch...")
        try:
            script = {
                "script": {
                    "lang": "painless",
                    "source": """
                        if (ctx._source.embeddings == null) {
                            ctx._source.embeddings = [:];
                        }
                        ctx._source.embeddings.work_experience = params.work_experience_embedding;
                    """,
                    "params": {
                        "work_experience_embedding": update_data["doc"].get("embeddings", {}).get("work_experience")
                    }
                },
                "query": {
                    "term": {
                        "person_id": user_id
                    }
                }
            }

            return self.opensearch_client.update_by_query(index=self.index_name, body=script)

        except Exception as e:
            logger.error(f"Error updating work_experience embedding for user_id {user_id}: {e}")
            return None
    
    def update_req_details_in_opensearch(self, document_id, data):
        try:
            logger.info(f"Started updating requisition details in OpenSearch for document_id: {document_id}")
            if not data:
                return None
            
            response = self.opensearch_client.update(
                index=self.index_name,
                id=document_id,
                body={
                    "doc": data
                }
            )
            logger.info(f"Successfully completed updated requisition details in OpenSearch for document_id: {document_id}")
            return response
        except Exception as e:
            logger.error(f"Error updating data in OpenSearch: {e}")
            return None
        finally:
            close_opensearch_client(self.opensearch_client)

    def update_status_in_opensearch(self, document_id):
        try:
            response = self.opensearch_client.update(
                index=self.index_name,
                id=document_id,
                body={
                    "doc": {
                        "status": 1
                    }
                }
            )
            return response
        except Exception as e:
            logger.error(f"Error updating status in OpenSearch: {e}")
            return None
        finally:
            close_opensearch_client(self.opensearch_client)


    def update_req_info_in_opensearch(self,document_id,recruiter_info):
        try:
            logger.info(f"Started updating recruiter_info in OpenSearch for document_id: {document_id}")
            if not recruiter_info:
                return None
            
            response = self.opensearch_client.update(
                index=self.index_name,
                id=document_id,
                body={
                    "doc": recruiter_info
                }
            )
            logger.info(f"Successfully completed updating recruiter_info in OpenSearch for document_id: {document_id}")
            return response
        except Exception as e:
            logger.error(f"Error updating recruiter_info in OpenSearch: {e}")
            return None
        finally:
            close_opensearch_client(self.opensearch_client)
