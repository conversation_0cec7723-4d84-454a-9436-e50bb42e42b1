from models.country import Country
from models.state import State
from models.city import City
from models.user import User    
from models.interview_feedback import InterviewFeedback
from models.recommended_candidates import RecommendedCandidates
from sqlalchemy import func
from fastapi.encoders import jsonable_encoder
from helper.helper import parse_data_for_dropdown,get_shortlisted_candidates_ids
from dependencies import CANDIDATE_THRESHOLD_SCORE,RECOMMENDED_CANDIDATE_THRESHOLD_SCORE

class DropDownServiceClass:
    def get_countries_for_dropdown(self, db):
        try:
            countries = (
                db.query(Country)
                .filter(Country.status == 1)
                .order_by(Country.name)
                .all()
            )
            return parse_data_for_dropdown(countries)
        except Exception as e:
            raise e

    def get_country_states(self, country_id, db):
        try:
            states = (
                db.query(State)
                .filter(State.country_id == country_id)
                .order_by(State.name)
                .all()
            )
            return parse_data_for_dropdown(states)
        except Exception as e:
            raise e
    
    def get_state_cities(self, state_id, db):
        try:
            cities = (
                db.query(City)
                .filter(City.state_id == state_id)
                .order_by(City.name)
                .all()
            )
            return parse_data_for_dropdown(cities)
        except Exception as e:
            raise e

    def get_suitable_candidates_countries(self, requisition_id, request, db):
        try:
            is_recommended = request.query_params.get("is_recommended", False)
            # Get all shortlisted candidate IDs
            shortlisted_candidate_ids = get_shortlisted_candidates_ids(requisition_id, db)
            
            if is_recommended:
                query = (
                    db.query(
                        Country.id.label("country_id"), 
                        Country.name.label("country_name"), 
                        City.id.label("city_id"), 
                        City.name.label("city_name"), 
                        func.count(RecommendedCandidates.user_id).label("candidate_count")
                    )
                    .join(User, User.id == RecommendedCandidates.user_id)
                    .join(Country, Country.id == User.country_id)
                    .join(City, City.id == User.city_id)
                    .filter(
                        RecommendedCandidates.requisition_id == requisition_id,
                        RecommendedCandidates.score >= RECOMMENDED_CANDIDATE_THRESHOLD_SCORE,
                        RecommendedCandidates.user_id.notin_(shortlisted_candidate_ids)
                        # RecommendedCandidates.satisfies_binary_requirements == True
                    )
                    .group_by(Country.id, City.id)
                    .order_by(func.count(RecommendedCandidates.user_id).desc())
                )
            else:
                # Query for other candidates
                query = (
                    db.query(
                        Country.id.label("country_id"), 
                        Country.name.label("country_name"), 
                        City.id.label("city_id"), 
                        City.name.label("city_name"), 
                        func.count(InterviewFeedback.user_id).label("candidate_count")
                    )
                    .join(User, User.id == InterviewFeedback.user_id)
                    .join(Country, Country.id == User.country_id)
                    .join(City, City.id == User.city_id)
                    .filter(
                        InterviewFeedback.requisition_id == requisition_id,
                        InterviewFeedback.score >= CANDIDATE_THRESHOLD_SCORE,
                        InterviewFeedback.status == 1,
                        InterviewFeedback.user_id.notin_(shortlisted_candidate_ids),
                        InterviewFeedback.satisfies_binary_requirements == True
                    )
                    .group_by(Country.id, City.id)
                    .order_by(func.count(InterviewFeedback.user_id).desc())
                )
            
            # Execute query and get results
            rows = query.all()
            
            # Process the results to group cities by country
            countries_dict = {}
            for row in rows:
                country_id = row.country_id
                
                if country_id not in countries_dict:
                    countries_dict[country_id] = {
                        "id": country_id,
                        "name": row.country_name,
                        "candidate_count": 0,
                        "cities": []
                    }
                
                # Add this city to the country's cities list
                countries_dict[country_id]["cities"].append({
                    "id": row.city_id,
                    "name": row.city_name,
                    "candidate_count": row.candidate_count
                })
                
                # Increment the country's total candidate count
                countries_dict[country_id]["candidate_count"] += row.candidate_count
            
            # Convert the dictionary to a list
            result = list(countries_dict.values())
            
                        # Sort countries by total candidate count
            result.sort(key=lambda x: x["candidate_count"], reverse=True)
            
            return jsonable_encoder(result)
        except Exception as e:
            raise e    