from models.organization_detail import OrganizationDetail
from models.recruiter import Recruiter
from models.organization import Organization
from schemas.organization_detail import OrganizationDetailsCreateSchema
from services.organization.organization import OrganizationServiceClass
from custom_exceptions import EntityAlreadyExistsException
from sqlalchemy.orm import joinedload
import logging
from fastapi.encoders import jsonable_encoder
import json


logger = logging.getLogger(__name__)

organization_service_object = OrganizationServiceClass

class OrganizationDetailsServiceClass:

    def add_organization_detail(
        self, request, organization_data: OrganizationDetailsCreateSchema, logo, db
    ):
        try:
            organization_input = {
                "name": organization_data.name,
                "city_id": organization_data.city_id,
                "logo": json.dumps(logo) if logo else None,
            }

            organization = organization_service_object.add_organization(self, organization_input, db)
            existing_organization = db.query(OrganizationDetail).filter(OrganizationDetail.organization_id == organization.id).first()
            update_user = (
                db.query(Recruiter)
                .filter(Recruiter.id == request.state.user.id)
                .update({"organization_id": organization.id})
            )
            db.commit()
            if existing_organization:
                return jsonable_encoder(existing_organization)

            organization_detail = OrganizationDetail(
                organization_id=organization.id,
                sub_title="",
                description=organization_data.description,
                founded=organization_data.founded,
                company_size=organization_data.company_size,
                revenue=organization_data.revenue,
                industry=organization_data.industry,
                address=organization_data.address,
                website= str(organization_data.website),
                specialities=organization_data.specialities,
            ) 
            db.add(organization_detail)
            db.commit() 
            db.refresh(organization_detail)          
            return jsonable_encoder(organization_detail)
        except Exception as e:
            raise e

    def update_orgacnization_detail(self, request, organization_detail_id ,organization_data: OrganizationDetailsCreateSchema, db):
        try:
            print("update_orgacnization_detail")
            organization = db.query(OrganizationDetail).filter(OrganizationDetail.id == organization_detail_id).first()

            if organization_data.address is not None:
                organization.address = organization_data.address
            if organization_data.company_size is not None:
                organization.company_size = organization_data.company_size
            if organization_data.description is not None:
                organization.description = organization_data.description
            if organization_data.founded is not None:
                organization.founded = organization_data.founded
            if organization_data.industry is not None:
                organization.industry = organization_data.industry
            if organization_data.revenue is not None:
                organization.revenue = organization_data.revenue
            if organization_data.website is not None:
                organization.website = organization_data.website
            if organization_data.specialities is not None:
                organization.specialities = organization_data.specialities

            db.commit() 
            db.refresh(organization)          
            return organization
        except Exception as e:
            raise e

    def get_organization_detail(self,request, db):
        try:
            organization = (
                db.query(Organization).options(
                    joinedload(Organization.organization_detail)
                ).filter(Organization.id == request.state.user.organization_id)
                .all()
            )
            return jsonable_encoder(organization)
        except Exception as e:
            raise e
