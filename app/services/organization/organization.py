from fastapi import HTTPException
from models.organization import Organization
from models.organization_detail import OrganizationDetail
from models.city import City
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session, joinedload
from fastapi.encoders import jsonable_encoder
from sqlalchemy import func

class OrganizationServiceClass:
    def get_organizations(self, profession_id, db):
        try:
            print("intit")
        except Exception as e:
            raise e

    def get_organization_by_name(self, name, db):
        try:
            organization = db.query(Organization).filter(
                Organization.name == name).first()
            return organization
        except Exception as e:
            raise e

    def add_organization(self, organization, db):
        try:
            name = organization.get("name")
            city_id = organization.get("city_id")
            logo = organization.get("logo")
            linkedin_id = organization.get("linkedin_id", "")
            domain = organization.get("domain", "")
            existing_organization = (
                db.query(Organization).filter(
                    func.lower(Organization.name) == name.lower()).first()
            )
            if existing_organization:
                return existing_organization
            else:
                organization = Organization(
                    name=name,
                    logo=logo,
                    city_id=city_id,
                    linkedin_id=linkedin_id, 
                    domain=domain
                )
                db.add(organization)
                db.commit()
                db.refresh(organization)
                return organization
        except Exception as e:
            raise e

    def get_organization(self, organization_id, db):
        try:
            organization = (
                db.query(Organization)
                .options(
                    joinedload(Organization.city),
                    joinedload(Organization.organization_detail),
                )
                .filter(Organization.id == organization_id)
                .first()
            )
            if organization:
                return jsonable_encoder(organization)
            else:
                raise HTTPException(
                    status_code=404, detail="Organization not found")
        except Exception as e:
            raise e
