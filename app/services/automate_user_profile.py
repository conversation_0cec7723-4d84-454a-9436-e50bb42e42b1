import csv
import pandas as pd
import logging
from models.user import User
from fastapi import HTTPException
from sqlalchemy.exc import SQLAlchemyError
from fastapi.encoders import jsonable_encoder
from custom_exceptions import EntityNotFoundException
import logging
from services.filemanager.filemanager import FileManagerClass
from dependencies import AWS_RESUME_FOLDER
from services.user.user import UserServiceClass
from services.parser.text_extractor_service import TextExtractorService
from services.external.openai import OpenAIServiceClass
from services.user.user_requisition import UserRequisitionServiceClass
from helper.helper import get_user_education_object, get_user_experience_object
import os
from models.requisition import Requisition
from services.requisition.requisition import RequisitionServiceClass
from services.auth import AuthServiceClass
from services.notification.email import send_email_background
from services.user.user import UserServiceClass
import requests
from io import BytesIO
from io import StringIO
from dependencies import (
    APP_HOST,
    USER_INVITE_URL,
    <PERSON><PERSON>_AUTOMATION_RESUME_FOLDER,
    <PERSON><PERSON>_BUCKET,
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    AWS_DEFAULT_REGION,
    CANDIDATE_THRESHOLD_SCORE,
    RESUME_THRESHOLD_SCORE,
)
import uuid
from services.interview.pre_evaluate import PreEvaluate
import boto3
from botocore.exceptions import NoCredentialsError, ClientError
import json
from datetime import datetime
from models.failed_invite import FailedInvite
from models.user_requisition import UserRequisition
from celery_tasks.send_interview_invite import send_invite
from celery_tasks.export_user_links import generate_invite_file

logger = logging.getLogger(__name__)
file_manager_object = FileManagerClass()


class InterviewInviteServiceClass:
    def __init__(self):
        pass

    def read_and_extract_variables(self, request, s3_url, background_tasks, db):
        try:
            response = requests.get(s3_url) 
            requisition_id = request.query_params.get("requisition_id", None)
            version = request.query_params.get("version", "v3")
            generate_file = bool(request.query_params.get("generate_file", False))
            pre_evaluation = bool(request.query_params.get("pre_evaluation", False))
            response.raise_for_status()

            requisition = (
                db.query(Requisition)
                .filter(Requisition.id == requisition_id)
                .first()
            )
            if not requisition:
                logger.error(f"Requisition not found with id: {requisition_id}")
                raise EntityNotFoundException("Requisition not found")

            df = pd.read_excel(BytesIO(response.content), header=1)
            logger.info(f"Process started with requisition Id: {requisition_id} version: {version}")
            logger.info(f"df had {df.head()}")
            for index, row in df.iterrows():
                first_name = row['First Name']
                last_name = row['Last Name']
                email = row['Email']
                number = row['Number']
                resume = row['Name of Resume File']
                linkdin_url = row['Profile URL']
                logger.info(
                    "================================ Row Data ================================ ")
                logger.info(f"Email: {email}")
                if not generate_file:
                    send_invite.delay(
                        requisition.id,
                        requisition.title,
                        first_name,
                        last_name,
                        email,
                        number,
                        resume,
                        linkdin_url,
                        version,
                        pre_evaluation,
                    )
                else:
                    logger.info("Generating file")
                    generate_invite_file.delay(
                        requisition.id,
                        requisition.title,
                        first_name,
                        last_name,
                        email,
                        number,
                        resume,
                        linkdin_url,
                        version,
                    )

        except Exception as e:
            logger.error(f"Error in read_and_extract_variables: {e}")
            raise e
