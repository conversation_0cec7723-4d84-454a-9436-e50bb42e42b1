from helper.helper import calculate_experience_years
from models.organization import OrganizationDetail
from services.interview.interview import InterviewServiceClass
from schemas.user import *
from schemas.user_education import UserEducationCreateUpdate
from schemas.user_experience import UserExperienceCreateUpdate
from models.user import User
from models.city import City
from models.country import Country
from models.school import School
from models.organization import Organization
from models.user_education import UserEducation
from models.user_experience import UserExperience
from models.user_experience_skill import UserExperienceSkill
from models.user_skill import UserSkill
from models.skill import Skill
from models.user_meta import UserMeta
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException, status
from fastapi.encoders import jsonable_encoder
from dependencies import PWD_CONTEXT, JWT_TOKEN_EXPIRY_TIME, returnResponse
import json
import random
import string
from typing import List
from services.auth import AuthServiceClass
from custom_exceptions import EntityAlreadyExistsException, EntityNotFoundException
from services.skills.skills import SkillsServiceClass
from sqlalchemy.orm import class_mapper, joinedload
from services.external.openai import OpenAIServiceClass
from services.organization.organization import OrganizationServiceClass
import logging
from models.recruiter import Recruiter
from schemas.recruiter import RecruiterCreate

logger = logging.getLogger(__name__)
interview_service = InterviewServiceClass()

class UserServiceClass:
    def hash_password(self, password: str):
        return PWD_CONTEXT.hash(password)

    def register_user(self, request: UserCreate, db):
        try:
            auth_service_object = AuthServiceClass()
            existing_user = db.query(User).filter(
                User.email == request.email).first()
            if existing_user:
                raise EntityAlreadyExistsException(
                    "User with this email already exists"
                )
            hashed_password = self.hash_password(request.password)
            user = User(
                first_name=request.first_name,
                last_name=request.last_name,
                phone=request.phone,
                email=request.email,
                password=hashed_password,
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            access_token = auth_service_object.create_login_access_token(
                data={"sub": user.email}, expires_delta=JWT_TOKEN_EXPIRY_TIME
            )
            user.access_token = access_token
            if not user:
                raise Exception("Unable to register user")
            return user
        except Exception as e:
            raise e

    def register_guest_user(self, request, db, mongodb_db):
        if not request.email:
            raise ValueError("Email is required for guest users.")

        existing_user = db.query(User).filter(User.email == request.email).first()
        if existing_user:
            raise EntityAlreadyExistsException("Email is already associated with an existing user.")

        guest_user = User(
            first_name="Guest",
            last_name="User",
            email=request.email,
            is_guest=True
        )
        db.add(guest_user)
        db.commit()
        db.refresh(guest_user)

        access_token = AuthServiceClass().create_login_access_token(
            data={"sub": guest_user.email},
            expires_delta=JWT_TOKEN_EXPIRY_TIME
        )

        return {"user": jsonable_encoder(guest_user), "access_token": access_token}

    def update_user(self, userData, request, db):
        try:
            user = db.query(User).filter(
                User.id == request.state.user.id).first()

            if userData.first_name is not None:
                user.first_name = userData.first_name
            if userData.last_name is not None:
                user.last_name = userData.last_name
            if userData.email is not None:
                user.email = userData.email
            if userData.profession_id is not None:
                user.profession_id = userData.profession_id
            if userData.phone is not None:
                user.phone = userData.phone
            if userData.expected_salary is not None:
                user.expected_salary = userData.expected_salary
            if userData.location_preferences is not None:
                user.location_preferences = userData.location_preferences
            if userData.notice_period is not None:
                user.notice_period = userData.notice_period
            if userData.nationality is not None:
                user.nationality = userData.nationality
            if userData.date_of_birth is not None:
                user.date_of_birth = userData.date_of_birth
            if userData.gender is not None:
                user.gender = userData.gender
            if userData.city_id is not None:
                user.city_id = userData.city_id

            db.add(user)
            db.commit()
            db.refresh(user)
            return user
        except Exception as e:
            raise e

    def complete_user_signup(self, userData, request, db):
        try:
            user = db.query(User).filter(
                User.id == request.state.user.id).first()

            user.expected_salary = userData.expected_salary
            user.location_preferences = userData.location_preferences
            user.notice_period = userData.notice_period
            if userData.linkedin_url is not None:
                user.linkedin_url = userData.linkedin_url
            db.add(user)
            db.commit()
            db.refresh(user)
            return user
        except Exception as e:
            raise e

    def update_user_preferences(self, payload, request, db):
        try:
            user = db.query(User).filter(User.id == request.state.user.id).first()

            user.expected_salary = payload.expected_salary
            user.location_preferences = payload.loc_preferences
            user.notice_period = payload.notice_period
            user.salary_negotiable = payload.salary_negotiable

            db.add(user)
            db.commit()
            db.refresh(user)
            self.save_user_meta(request, payload, db)
            return user
        except Exception as e:
            raise e

    def update_education(
        self, educations: List[UserEducationCreateUpdate], request, db
    ):
        user_id = request.state.user.id
        user_education_response = []
        try:
            for education in educations:

                if hasattr(education, "id"):  # Check if ID is provided
                    # Try to find the existing record to update
                    user_education = (
                        db.query(UserEducation)
                        .filter(
                            UserEducation.id == education.id,
                            UserEducation.user_id == user_id,
                        )
                        .first()
                    )

                    if user_education:
                        user_education.school_id = education.school_id
                        user_education.degree = education.degree
                        user_education.field = education.field
                        user_education.description = education.description
                        user_education.grade = education.grade
                        # user_education.city_id = education.city_id
                        user_education.start_date = education.start_date
                        user_education.end_date = education.end_date
                        user_education.location=education.location
                    else:
                        # Handle the case where the record is not found (optional)
                        raise EntityNotFoundException(
                            f"UserEducation with id {education.id} not found."
                        )
                else:
                    # Create new record if no ID is provided
                    user_education = UserEducation(
                        user_id=user_id,
                        school_id=education.school_id,
                        degree=education.degree,
                        field=education.field,
                        description=education.description,
                        grade=education.grade,
                        city_id=0,
                        start_date=education.start_date,
                        end_date=education.end_date,
                        location=education.location
                    )
                    db.add(user_education)

                db.commit()
                db.refresh(user_education)
                user_education_response.append(user_education)

            return user_education_response

        except SQLAlchemyError as e:
            # db.rollback()
            raise e

        except Exception as e:
            # db.rollback()
            raise e

    def update_experience(
        self, experiences: List[UserExperienceCreateUpdate], request, db
    ):
        user_id = request.state.user.id
        user_experience_response = []
        try:
            for experience in experiences:

                if hasattr(experience, "id"):  # Check if ID is provided
                    # Try to find the existing record to update
                    user_experience = (
                        db.query(UserExperience)
                        .filter(
                            UserExperience.id == experience.id,
                            UserExperience.user_id == user_id,
                        )
                        .first()
                    )
                    if user_experience:
                        user_experience.organization_id = experience.organization_id
                        user_experience.job_title = experience.job_title
                        user_experience.emp_type = experience.emp_type
                        user_experience.description = experience.description
                        user_experience.from_date = experience.from_date
                        user_experience.to_date = experience.to_date
                        user_experience.city_id = 0
                        user_experience.location_type = experience.location_type
                        user_experience.location = experience.location

                    else:
                        # Handle the case where the record is not found (optional)
                        raise EntityNotFoundException(
                            f"User Experience with id {experience.id} not found."
                        )
                else:
                    user_experience = UserExperience(
                        user_id=user_id,
                        organization_id=experience.organization_id,
                        job_title=experience.job_title,
                        emp_type=experience.emp_type,
                        description=experience.description,
                        from_date=experience.from_date,
                        to_date=experience.to_date,
                        city_id=0,
                        location_type=experience.location_type,
                        location = experience.location
                    )
                    db.add(user_experience)

                db.commit()
                db.refresh(user_experience)
                user_experience_response.append(user_experience)
            return user_experience_response

        except Exception as e:
            raise e

    def __updateUserExperienceSkill(self, userExperience, skills, db):
        try:
            for skill in skills:
                user_experience_skill = (
                    db.query(UserExperienceSkill)
                    .filter(
                        UserExperienceSkill.user_experience_id == userExperience.id,
                        UserExperienceSkill.skill_id == skill,
                    )
                    .first()
                )
                if user_experience_skill:
                    continue
                else:
                    user_experience_skill = UserExperienceSkill(
                        user_experience_id=userExperience.id, skill_id=skill
                    )
                    db.add(user_experience_skill)
                    db.commit()
                    db.refresh(user_experience_skill)
            return True
        except Exception as e:
            raise e

    def update_user_resume_detail(self, user_object_to_update: dict, request, db):
        try:
            user = db.query(User).filter(
                User.id == request.state.user.id).first()
            user.about = user_object_to_update["about"]
            user.total_experience = user_object_to_update["total_experience"]
            user.resume_link = user_object_to_update["resume_link"]
            user.city_id = user_object_to_update["city_id"]
            user.country_id = user_object_to_update["country_id"]
            user.state_id = user_object_to_update["state_id"]
            user.location = user_object_to_update["location"]
            if user.linkedin_url is None or user.linkedin_url.strip() == "":
                user.linkedin_url = user_object_to_update["linkedin"]
            db.add(user)
            db.commit()
            db.refresh(user)
            return user
        except Exception as e:
            raise e

    def update_user_profile_picture(self, profile_picture_link, request, db):
        """
        Update user's profile picture link in the database.

        Args:
            profile_picture_link (str): The link of the profile picture
            request (Request): The request object
            db (Session): The database session

        Returns:
            User: The updated user object
        """
        try:
            logger.info(
                f"Updating user profile picture link: {profile_picture_link}")
            # Get the user by ID
            user = db.query(User).filter(
                User.id == request.state.user.id).first()
            logger.info(profile_picture_link)
            # Update the user's profile picture link
            user.profile_pic = json.dumps(profile_picture_link)
            db.add(user)
            db.commit()
            db.refresh(user)
            logger.info(
                f"User profile picture link updated successfully: {user.profile_pic}")
            return user
        except Exception as e:
            raise e

    def get_user(self, request, db):
        try:
            user = db.query(User).filter(
                User.id == request.state.user.id).first()
            return user
        except Exception as e:
            raise e

    def delete_user(self, userEmail, request, db):
        try:
            user = db.query(User).filter(User.email == userEmail).first()
            if user:
                # Delete UserEducation records for the user
                db.query(UserEducation).filter(UserEducation.user_id == user.id).delete(
                    synchronize_session=False
                )
                user_experiences = (
                    db.query(UserExperience)
                    .filter(UserExperience.user_id == user.id)
                    .all()
                )

                # Extract all user_experience_ids
                user_experience_ids = [ue.id for ue in user_experiences]
                if user_experience_ids:
                    # Delete from UserExperienceSkill where user_experience_id is in the user_experience_ids list
                    db.query(UserExperienceSkill).filter(
                        UserExperienceSkill.user_experience_id.in_(
                            user_experience_ids)
                    ).delete(synchronize_session=False)
                    # Delete from UserExperience where user_id equals the given user_id
                    db.query(UserExperience).filter(
                        UserExperience.user_id == user.id
                    ).delete(synchronize_session=False)
                db.delete(user)
                db.commit()
            else:
                return False
            return True
        except Exception as e:
            db.rollback()
            raise e

    def create_update_user_skill(self, skills_array, request, db):
        try:
            skill_service = SkillsServiceClass()
            user_id = request.state.user.id
            for skill in skills_array:
                skill_object_for_service = {
                    "name": skill,
                    "description": None,
                }
                system_skill = skill_service.create_skills(
                    skill_object_for_service, db)
                user_skill = (
                    db.query(UserSkill)
                    .filter(
                        UserSkill.user_id == user_id,
                        UserSkill.skill_id == system_skill.id,
                    )
                    .first()
                )
                if not user_skill:
                    user_skill = UserSkill(
                        user_id=user_id, skill_id=system_skill.id)
                    db.add(user_skill)
                    db.commit()
                    db.refresh(user_skill)
            return True
        except Exception as e:
            raise e

    def get_user_detail(self, user_id, db):
        try:
            user = db.query(User).options(
                joinedload(User.educations).joinedload(UserEducation.school),
                joinedload(User.experiences).joinedload(
                    UserExperience.organization),
                joinedload(User.skills).joinedload(UserSkill.skill)
            ).filter(User.id == user_id).first()

            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            return jsonable_encoder(user)

        except Exception as e:
            raise e

    def get_user_details(self, request, db):
        try:
            user = self.get_user(request, db)
            user_education = (
                db.query(
                    UserEducation.degree.label("degree"),
                    UserEducation.field.label("field"),
                    UserEducation.start_date.label("start_date"),
                    UserEducation.end_date.label("end_date"),
                    UserEducation.location.label("location"),
                    School.name.label("school"),
                )
                .join(School, UserEducation.school_id == School.id, isouter=True)
                .filter(UserEducation.user_id == user.id)
                .all()
            )
            user_experience = (
                db.query(
                    UserExperience.job_title.label("job_title"),
                    UserExperience.emp_type.label("emp_type"),
                    UserExperience.from_date.label("from_date"),
                    UserExperience.to_date.label("to_date"),
                    UserExperience.location.label("location"),
                    Organization.name.label("organization"),
                )
                .join(
                    Organization,
                    UserExperience.organization_id == Organization.id,
                    isouter=True,
                )
                .filter(UserExperience.user_id == user.id)
                .all()
            )

            user_skills = db.query(Skill.name.label("name")).join(
                UserSkill, Skill.id == UserSkill.skill_id).filter(UserSkill.user_id == user.id).all()

            user_education = [dict(zip(("degree", "field", "start_date", "end_date",
                                        "location", "school"), result)) for result in user_education]

            user_experience = [
                dict(
                    zip(
                        (
                            "job_title",
                            "emp_type",
                            "from_date",
                            "to_date",
                            "location",
                            "organization",
                        ),
                        result,
                    )
                )
                for result in user_experience
            ]

            user_skills = [result[0] for result in user_skills]

            return {
                "education": user_education,
                "experience": user_experience,
                "skills": user_skills,
            }
        except Exception as e:
            raise e

    def register_recruiter_user(self, request: RecruiterCreate, db):
        try:
            auth_service_object = AuthServiceClass()
            existing_user = db.query(Recruiter).filter(
                Recruiter.email == request.email).first()
            if existing_user:
                raise EntityAlreadyExistsException(
                    "Recruiter with this email already exists"
                )
            hashed_password = self.hash_password(request.password)
            user = Recruiter(
                first_name=request.first_name,
                last_name=request.last_name,
                phone=request.phone,
                email=request.email,
                password=hashed_password,
                organization_id=0,  # To avoid null conflict
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            access_token = auth_service_object.create_login_access_token(
                data={"sub": user.email}, expires_delta=JWT_TOKEN_EXPIRY_TIME
            )
            user.access_token = access_token
            if not user:
                raise Exception("Unable to register user")
            return user
        except Exception as e:
            raise e

    def get_user_by_id(self, user_id, db):
        """
        Get a user by user ID.

        Args:
            user_id (int): The ID of the user to retrieve.
            db (Session): The database session to use.

        Returns:
            User: The user object, or None if not found.
        """

        try:
            user = db.query(User).filter(User.id == user_id).first()
            return user
        except Exception as e:
            raise e

    def register_user_revamp(self, request: UserCreateRevamp, db):
        try:
            auth_service_object = AuthServiceClass()
            existing_user = db.query(User).filter(
                User.email == request.email).first()
            if existing_user:
                raise EntityAlreadyExistsException(
                    "User with this email already exists"
                )
            hashed_password = self.hash_password(request.password)
            user = User(
                    first_name='',
                    last_name='',
                    # phone=request.phone,
                    email=request.email,
                    password=hashed_password,
                )
            db.add(user)
            db.commit()
            db.refresh(user)
            access_token = auth_service_object.create_login_access_token(
                data={"sub": user.email}, expires_delta=JWT_TOKEN_EXPIRY_TIME
            )
            user.access_token = access_token
            if not user:
                raise Exception("Unable to register user")
            return user
        except Exception as e:
            raise e

    def save_user_meta(self, request, payload, db):
        try:
            user_meta = UserMeta(
                user_id=request.state.user.id, key=payload.key, value=payload.value
            )
            db.add(user_meta)
            db.commit()
            db.refresh(user_meta)
            return jsonable_encoder(user_meta)
        except Exception as e:
            raise e

    def insert_single_user_to_opensearch(self, user_id, db):
        try:
            user_data = interview_service.convert_data(user_id, db)
            if user_data:
                interview_service.insert_record_to_opensearch(user_data, db)
                db.query(User).filter(User.id == user_id).update(
                    {"opensearch_status": 1}, synchronize_session=False
                )
                db.commit()
        except Exception as e:
            logger.error(f"Error processing user_id={user_id}: {e}")
            db.rollback()
        logger.info("Inserted single user to opensearch and updating opensearch status to 1 in postgres")
        
        return True
    

    def get_user_experience_list(self, user_id, db):
        logger.info("Get user experiences list from db to update in opensearch")
        """
        Fetch and return the work experience list for a given user ID.
        """

        work_experiences = db.query(
            Organization.name.label("organization"),
            OrganizationDetail.description.label("organization_description"),
            UserExperience.job_title,
            UserExperience.location_type,
            UserExperience.from_date,
            UserExperience.to_date,
            UserExperience.emp_type,
            UserExperience.description.label("experience_description")
        ).join(
            Organization, UserExperience.organization_id == Organization.id
        ).outerjoin(
            OrganizationDetail, Organization.id == OrganizationDetail.organization_id
        ).filter(
            UserExperience.user_id == user_id
        ).all()

        experience_list = []
        for exp in work_experiences:
            experience_list.append({
                "organization": exp.organization or "",
                "job_title": exp.job_title or "",
                "location_type": exp.location_type or "",
                "from_date": exp.from_date,
                "to_date": exp.to_date,
                "emp_type": exp.emp_type or "",
                "description": exp.experience_description or "",
                "relevant_experience": calculate_experience_years(exp.from_date, exp.to_date),
                "about_company": exp.organization_description or "",
            })

        return experience_list
