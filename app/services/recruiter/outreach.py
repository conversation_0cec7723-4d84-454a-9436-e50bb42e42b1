from fastapi.encoders import jsonable_encoder
from services.notification.email import send_email_with_celery
from models.recruiter_outreach import RecruiterOutreach
import logging
from dependencies import RECRUITER_OUTREACH_MAIL_FROM, RECRUITER_OUTREACH_REPLY_TO

logger = logging.getLogger(__name__)
class RecruiterOutreachServiceClass:

    def create_recruiter_outreach_activity(self, recruiter_outreache_data, db):
        try:
            check_if_outreach_exists = (
                db.query(RecruiterOutreach)
                .filter(RecruiterOutreach.requisition_id == recruiter_outreache_data['requisition_id'])
                .first()
            )
            if not check_if_outreach_exists:
                outreach_data = RecruiterOutreach(
                        requisition_id=recruiter_outreache_data['requisition_id'],
                        receiver_email=recruiter_outreache_data['receiver_email'],
                        cc_emails=None,
                        ai_subject=recruiter_outreache_data['ai_subject'],
                        ai_content=recruiter_outreache_data['ai_content'],
                        human_subject = None,
                        human_content = None,
                        sent_email_count = 0,
                        status = 0,
                        hash=recruiter_outreache_data['hash'],
                )
                db.add(outreach_data)
                db.commit()
                db.refresh(outreach_data)
                return jsonable_encoder(outreach_data)
        except Exception as e:
            raise e

    def get_recruiter_outreach_activity_by_id(self, outreach_id, db):
        try:
            outreach_data = db.query(RecruiterOutreach).filter(RecruiterOutreach.id == outreach_id).first()
            return outreach_data
        except Exception as e:
            raise e

    def get_recruiter_outreach_by_status(self, status, db):
        try:
            outreach_data = (
                db.query(RecruiterOutreach)
                .filter(RecruiterOutreach.status == status)
                .all()
            )
            return outreach_data
        except Exception as e:
            raise e

    def send_recruiter_outreach_email(self, outreach_id, db):
        try:
            logger.info(f"send email from route task init")
            outreach_data = self.get_recruiter_outreach_activity_by_id(outreach_id, db)
            cc_emails = (
                outreach_data.cc_emails.split(",") if outreach_data.cc_emails else []
            )
            logger.info(f"CC Emails: {cc_emails}")
            res = send_email_with_celery(
                outreach_data.human_subject,
                outreach_data.receiver_email,
                {"data": outreach_data.human_content},
                "recruiter_cold_email.html",
                RECRUITER_OUTREACH_MAIL_FROM,
                cc_emails,
                RECRUITER_OUTREACH_REPLY_TO,
            )
            if res:
                outreach_data.sent_email_count += 1
                outreach_data.status = RecruiterOutreach.EMAIL_SENT
            db.commit()
            db.refresh(outreach_data)
        except Exception as e:
            raise e
