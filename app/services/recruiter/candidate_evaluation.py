from models.interview_feedback import InterviewFeedback
from models.interview import Interview
from models.user import User
from models.city import City
from sqlalchemy.orm import class_mapper, joinedload
from custom_exceptions import AuthFailedException, EntityNotFoundException
from models.shortlisted_candidate import ShortlistedCandidate
from fastapi.encoders import jsonable_encoder
from models.question_feedback import QuestionFeedback
from models.user_education import UserEducation
from models.user_experience import UserExperience
from models.requisition import Requisition
from models.recommended_candidates import RecommendedCandidates
from models.country import Country
from models.user_skill import UserSkill
from services.requisition.requisition import RequisitionServiceClass
from services.filemanager.filemanager import FileManagerClass
from dependencies import AWS_VIDEO_FOLDER, CANDIDATE_THRESHOLD_SCORE,RECOMMENDED_CANDIDATE_THRESHOLD_SCORE, CONTACT_US_EMAIL
from services.notification.email import send_email_background
import os
import math
import json
from sqlalchemy import func 
from helper.helper import get_shortlisted_candidates_ids

class CandidateEvaluationServiceClass:
    def __init__(self):
        pass

    def get_suitable_candidates(self, requisition_id, request, db):
        try:
            page = int(request.query_params.get("page", 1))
            per_page = int(request.query_params.get("per_page", 10))

            country_id = request.query_params.get("country_id")
            country_id = int(country_id) if country_id and country_id.isdigit() else None

            city_id = request.query_params.get("city_id")
            city_id = int(city_id) if city_id and city_id.isdigit() else None

            shorlisted_candidates = get_shortlisted_candidates_ids(
                requisition_id, db
            )

            query = (
                db.query(InterviewFeedback)
                .join(InterviewFeedback.user)  # Join with the User model to filter on User.city_id
                .options(joinedload(InterviewFeedback.user)
                         )  # Optimize loading of the user and city relationships
                .filter(
                    InterviewFeedback.requisition_id == requisition_id,
                    InterviewFeedback.score >= CANDIDATE_THRESHOLD_SCORE,
                    InterviewFeedback.status == InterviewFeedback.STATUS_APPROVED,
                    InterviewFeedback.user_id.notin_(shorlisted_candidates),
                )
                .order_by(InterviewFeedback.score.desc(), InterviewFeedback.id.asc())
            )

            if country_id is None:
                query = query.filter(InterviewFeedback.satisfies_binary_requirements == True)
            else:
                if country_id is not None and city_id is None:
                    city_ids = [city.id for city in db.query(City).filter(City.country_id == country_id).all()]
                    query = query.filter(User.city_id.in_(city_ids))
                else:
                    query = query.filter(User.city_id == city_id)

            total = query.count()
            interview_feedbacks = (
                query.offset((page - 1) * per_page)
                .limit(per_page)
                .all()
            )

            for interview_feedback in interview_feedbacks:
                # No need for the following code as it is already done in the model
                # interview_feedback.user_video_links = self.get_candidate_videos_from_s3(
                #     interview_feedback.user_id, interview_feedback.interview_id, db
                # )
                interview_feedback.questions = (
                    db.query(QuestionFeedback)
                    .filter(
                        QuestionFeedback.interview_id == interview_feedback.interview_id
                    )
                    .order_by(QuestionFeedback.id.asc())
                    .all()
                )

                interview_feedback.feedback = json.loads(interview_feedback.feedback)
                interview_feedback.shortlisted_candidate = (
                    db.query(ShortlistedCandidate)
                    .filter(
                        ShortlistedCandidate.candidate_id == interview_feedback.user_id,
                        ShortlistedCandidate.requisition_id == requisition_id,
                    )
                    .first()
                )
            return {
                "pagination": {
                    "total": total,
                    "last_page": math.ceil(total / per_page),
                    "page": page,
                    "per_page": per_page,
                },
                "items": jsonable_encoder(interview_feedbacks),
            }

        except Exception as e:
            raise e

    def get_candidate(self, interview_feedback_id, db):
        try:
            interview_feedback = (
                db.query(InterviewFeedback)
                .options(
                    joinedload(InterviewFeedback.user)
                    .joinedload(User.educations),
                    joinedload(InterviewFeedback.user)
                    .joinedload(User.experiences),
                    joinedload(InterviewFeedback.user),
                    joinedload(InterviewFeedback.user)
                    .joinedload(User.educations)
                    .joinedload(UserEducation.school),
                    joinedload(InterviewFeedback.user)
                    .joinedload(User.experiences)
                    .joinedload(UserExperience.organization),
                    joinedload(InterviewFeedback.user)
                    .joinedload(User.skills).joinedload(UserSkill.skill)
                )
                .filter(
                    InterviewFeedback.id == interview_feedback_id,
                    # InterviewFeedback.status == InterviewFeedback.STATUS_APPROVED,
                )
                .first()
            )
            questions_feedback = (
                db.query(QuestionFeedback)
                .filter(
                    QuestionFeedback.interview_id == interview_feedback.interview_id
                )
                .order_by(QuestionFeedback.id.asc())
                .all()
            )

            interview_feedback.questions = questions_feedback

            # interview_feedback.user_video_links = self.get_candidate_videos_from_s3(
            #     interview_feedback.user_id, interview_feedback.interview_id, db
            # )

            interview_feedback.feedback = json.loads(interview_feedback.feedback)
            interview_feedback.shortlisted_candidate = (
                db.query(ShortlistedCandidate)
                .filter(
                    ShortlistedCandidate.candidate_id == interview_feedback.user_id,
                    ShortlistedCandidate.requisition_id == interview_feedback.requisition_id,
                )
                .first()
            )
            return jsonable_encoder(interview_feedback)
        except Exception as e:
            raise e

    def take_action_on_profile(
        self, interview_feedback_input, request, background_tasks, db
    ):
        try:
            candidate_type = request.query_params.get("type", "interviewed_candidates")
            
            # Handle different candidate types
            if candidate_type == "recommended":
                entity = (
                    db.query(RecommendedCandidates)
                    .filter(
                        RecommendedCandidates.id
                        == interview_feedback_input.interview_feedback_id
                    )
                    .first()
                )
            elif candidate_type == "non-interviewed-candidates":
                # For non-interviewed candidates, we don't need to query for an entity
                # as interview_feedback_id will be 0
                entity = None
            else:  # default: interviewed_candidates
                entity = (
                    db.query(InterviewFeedback)
                    .filter(
                        InterviewFeedback.id
                        == interview_feedback_input.interview_feedback_id
                    )
                    .first()
                )

            # For non-interviewed candidates, we don't validate entity existence
            if candidate_type != "non-interviewed-candidates" and not entity:
                raise EntityNotFoundException("Invalid Entity")
                
            # Get or create shortlisted candidate
            if candidate_type == "non-interviewed-candidates":
                # For non-interviewed candidates, directly check if candidate already shortlisted
                shorlisted_candidate = (
                    db.query(ShortlistedCandidate)
                    .filter(
                        ShortlistedCandidate.candidate_id == interview_feedback_input.user_id,
                        ShortlistedCandidate.requisition_id == 0
                    )
                    .first()
                )
                
                if not shorlisted_candidate:
                    # Create new shortlisted candidate with interview_id=0
                    shorlisted_candidate = ShortlistedCandidate(
                        interview_id=0,  # Zero for non-interviewed candidates
                        requisition_id=0,
                        candidate_id=interview_feedback_input.user_id,
                        recruiter_id=request.state.user.id,
                        status=interview_feedback_input.status,
                        feedback=interview_feedback_input.feedback,
                    )
                    db.add(shorlisted_candidate)
                else:
                    shorlisted_candidate.status = interview_feedback_input.status
                    shorlisted_candidate.feedback = interview_feedback_input.feedback
                
            else:
                # Original logic for interviewed and recommended candidates
                shorlisted_candidate = (
                    db.query(ShortlistedCandidate)
                    .filter(
                        ShortlistedCandidate.interview_id == entity.interview_id,
                        ShortlistedCandidate.candidate_id == entity.user_id,
                    )
                    .first()
                )
                if not shorlisted_candidate:
                    shorlisted_candidate = ShortlistedCandidate(
                        interview_id=entity.interview_id,
                        requisition_id=entity.requisition_id,
                        candidate_id=entity.user_id,
                        recruiter_id=request.state.user.id,
                        status=interview_feedback_input.status,
                        feedback=interview_feedback_input.feedback,
                    )
                    db.add(shorlisted_candidate)
                else:
                    shorlisted_candidate.status = interview_feedback_input.status
                    shorlisted_candidate.feedback = interview_feedback_input.feedback
            
            db.commit()
            db.refresh(shorlisted_candidate)

            # send email on shortlist candidate
            # if (
            #     interview_feedback_input.status
            #     == ShortlistedCandidate.STATUS_INTERVIEW_SCHEDULED
            # ):
            #     self.__send_email_to_team(
            #         request, shorlisted_candidate, background_tasks, db
            #     )
            if candidate_type != 'non-interviewed-candidates':
                self.__send_email_to_team(
                    request,
                    shorlisted_candidate,
                    background_tasks,
                    db,
                    interview_feedback_input.status,
                )

            return shorlisted_candidate
        except Exception as e:
            raise e

    def get_candidate_videos_from_s3(self, user_id, interview_id, db):
        try:
            # interview_feedback = (
            #     db.query(InterviewFeedback)
            #     .filter(InterviewFeedback.id == interview_feedback_id)
            #     .first()
            # )
            video_links = FileManagerClass().get_s3_contents(
                AWS_VIDEO_FOLDER + str(user_id) + "/" + str(interview_id) + "/"
            )
            return video_links
        except Exception as e:
            raise e

    def get_shortlisted_candidates(self, request, db):
        try:
            page = int(request.query_params.get("page", 1))
            per_page = int(request.query_params.get("per_page", 10))
            requisition_id = request.query_params.get("requisition_id", None)
            status = request.query_params.get("status")

            if not requisition_id:
                requsition_ids_of_current_recruiter = (
                    self.__get_requsition_ids_of_current_recruiter(request, db)
                )
            else:
                requsition_ids_of_current_recruiter = [requisition_id]

            query = (
                db.query(ShortlistedCandidate)
                .options(
                    joinedload(ShortlistedCandidate.user),
                    joinedload(ShortlistedCandidate.requisition),
                )
                .filter(
                    ShortlistedCandidate.requisition_id.in_(
                        requsition_ids_of_current_recruiter
                    ),
                    ShortlistedCandidate.recruiter_id == request.state.user.id,
                    ShortlistedCandidate.status == status
                )
            )
            total = query.count()
            shortlisted_candidates = (
                query.offset((page - 1) * per_page).limit(per_page).all()
            )

            for shortlisted_candidate in shortlisted_candidates:

                shortlisted_candidate.questions = (
                    db.query(QuestionFeedback)
                    .filter(
                        QuestionFeedback.interview_id
                        == shortlisted_candidate.interview_id
                    )
                    .order_by(QuestionFeedback.id.asc())
                    .all()
                )

                check_for_interview = (
                    db.query(Interview)
                    .filter(
                        Interview.requisition_id
                        == shortlisted_candidate.requisition_id,
                        Interview.user_id
                        == shortlisted_candidate.candidate_id,
                    )
                    .first()
                )
                shortlisted_candidate.is_recommended_candidate = (
                    False if check_for_interview else True
                )
                if check_for_interview:
                    shortlisted_candidate.feedback_id = (
                        db.query(InterviewFeedback)
                        .filter(
                            InterviewFeedback.requisition_id
                            == shortlisted_candidate.requisition_id,
                            InterviewFeedback.user_id
                            == shortlisted_candidate.candidate_id,
                        )
                        .first()
                        .id
                    )
                else:
                    shortlisted_candidate.feedback_id = (
                        db.query(RecommendedCandidates)
                        .filter(
                            RecommendedCandidates.requisition_id
                            == shortlisted_candidate.requisition_id,
                            RecommendedCandidates.user_id
                            == shortlisted_candidate.candidate_id,
                        )
                        .first()
                        .id
                    )

            return {
                "pagination": {
                    "total": total,
                    "last_page": math.ceil(total / per_page),
                    "page": page,
                    "per_page": per_page,
                },
                "items": jsonable_encoder(shortlisted_candidates),
            }
        except Exception as e:
            raise e

    def __get_requsition_ids_of_current_recruiter(self, request, db):
        try:
            requisition_ids = (
                db.query(Requisition.id)
                .filter(
                    Requisition.organization_id == request.state.user.organization_id
                )
                .all()
            )
            requisition_ids = [req_id[0] for req_id in requisition_ids]
            return requisition_ids
        except Exception as e:
            raise e

    def action_on_shortlisted_candidate(self, shortlisted_candidate_id, payload, db):
        try:
            shortlisted_candidate = (
                db.query(ShortlistedCandidate)
                .filter(ShortlistedCandidate.id == shortlisted_candidate_id)
                .first()
            )
            if not shortlisted_candidate:
                raise EntityNotFoundException("Shortlisted candidate not found")

            shortlisted_candidate.status = payload.status
            db.commit()
            return True
        except Exception as e:
            raise e

    def get_recommended_candidates(self, requisition_id, request, db):
        try:
            page = int(request.query_params.get("page", 1))
            per_page = int(request.query_params.get("per_page", 10))

            country_id = request.query_params.get("country_id")
            country_id = int(country_id) if country_id and country_id.isdigit() else None

            city_id = request.query_params.get("city_id")
            city_id = int(city_id) if city_id and city_id.isdigit() else None

            shorlisted_candidates = get_shortlisted_candidates_ids(
                requisition_id, db
            )

            query = (
                db.query(RecommendedCandidates)
                .join(RecommendedCandidates.user)
                .options(joinedload(RecommendedCandidates.user))
                .filter(
                    RecommendedCandidates.requisition_id == requisition_id,
                    RecommendedCandidates.score >= RECOMMENDED_CANDIDATE_THRESHOLD_SCORE,
                    RecommendedCandidates.user_id.notin_(shorlisted_candidates),
                )
                .order_by(
                    RecommendedCandidates.score.desc(), RecommendedCandidates.id.asc()
                )
            )

            if city_id is not None:
                query = query.filter(User.city_id == city_id)
            elif country_id is not None:
                city_ids = [city.id for city in db.query(City.id).filter(City.country_id == country_id).all()]
                if city_ids:
                    query = query.filter(User.city_id.in_(city_ids))

            total = query.count()

            recommended_candidates = (
                query.order_by(RecommendedCandidates.score.desc())
                .offset((page - 1) * per_page)
                .limit(per_page)
                .all()
            )

            for recommended_candidate in recommended_candidates:
                # recommended_candidate.user_video_links = (
                #     self.get_candidate_videos_from_s3(
                #         recommended_candidate.user_id,
                #         recommended_candidate.interview_id,
                #         db,
                #     )
                # )
                recommended_candidate.questions = (
                    db.query(QuestionFeedback)
                    .filter(
                        QuestionFeedback.interview_id == recommended_candidate.interview_id
                    )
                    .order_by(QuestionFeedback.id.asc())
                    .all()
                )
                recommended_candidate.shortlisted_candidate = (
                    db.query(ShortlistedCandidate)
                    .filter(
                        ShortlistedCandidate.candidate_id
                        == recommended_candidate.user_id,
                        ShortlistedCandidate.requisition_id == requisition_id,
                    )
                    .first()
                )
            return {
                "pagination": {
                    "total": total,
                    "last_page": math.ceil(total / per_page),
                    "page": page,
                    "per_page": per_page,
                },
                "items": jsonable_encoder(recommended_candidates),
            }

        except Exception as e:
            raise e

    def get_recommended_candidate(self, recommended_candidate_id, db):
        try:
            recommended_candidate = (
                db.query(RecommendedCandidates)
                .options(
                    joinedload(RecommendedCandidates.user)
                    .joinedload(User.educations),
                    joinedload(RecommendedCandidates.user)
                    .joinedload(User.experiences),
                    joinedload(RecommendedCandidates.user),
                    joinedload(RecommendedCandidates.user)
                    .joinedload(User.educations)
                    .joinedload(UserEducation.school),
                    joinedload(RecommendedCandidates.user)
                    .joinedload(User.experiences)
                    .joinedload(UserExperience.organization),
                )
                .filter(
                    RecommendedCandidates.id == recommended_candidate_id,
                )
                .first()
            )
            questions_feedback = (
                db.query(QuestionFeedback)
                .filter(
                    QuestionFeedback.interview_id == recommended_candidate.interview_id
                )
                .order_by(QuestionFeedback.id.asc())
                .all()
            )

            recommended_candidate.questions = questions_feedback

            # This is temporary fix to handle the feedback field
            try:
                recommended_candidate.feedback = json.loads(recommended_candidate.feedback)
            except (TypeError, json.JSONDecodeError):
                recommended_candidate.feedback = recommended_candidate.feedback

            recommended_candidate.shortlisted_candidate = (
                db.query(ShortlistedCandidate)
                .filter(
                    ShortlistedCandidate.candidate_id == recommended_candidate.user_id,
                    ShortlistedCandidate.requisition_id == recommended_candidate.requisition_id,
                )
                .first()
            )
            # recommended_candidate.feedback = json.loads(
            #     interview_feedback.feedback)
            return jsonable_encoder(recommended_candidate)
        except Exception as e:
            raise e

    def __send_email_to_team(self, request, shorlisted_candidate, background_tasks, db, status):
        try:
            type = ''
            
            requisition = RequisitionServiceClass().get_requisition(
                request, shorlisted_candidate.requisition_id, db
            )
            
            if status == ShortlistedCandidate.STATUS_INTERVIEW_SCHEDULED:
                type = "Shortlisted"
                subject = f"Candidate {type} – Schedule Interview with {str(request.state.user.first_name + ' ' + request.state.user.last_name)}"
                content = f"We’re excited to inform you that a candidate has been shortlisted for the <strong>{requisition['title']}</strong> role at <strong>{requisition['organization']['name']}</strong>. Please arrange the interview between the shortlisted candidate and the recruiter, from {requisition['organization']['name']}, at your earliest convenience."
            elif status == ShortlistedCandidate.STATUS_REJECTED:
                type = "Rejected"
                subject = f"Candidate {type} by {str(request.state.user.first_name + ' ' + request.state.user.last_name)}"
                content = f"The following candidate has been rejected for the <strong>{requisition['title']}</strong> role by the recruiter from <strong>{requisition['organization']['name']}</strong>"
            
            elif status == ShortlistedCandidate.STATUS_SAVED_PIPELINE:
                type = "Saved to Pipeline"
                subject = f"Candidate {type} by {str(request.state.user.first_name + ' ' + request.state.user.last_name)}"
                content = f"The following candidate has been saved in the pipeline to be later considered for the interview for <strong>{requisition['title']}</strong> role by the recruiter from <strong>{requisition['organization']['name']}</strong>"
                
            elif status == ShortlistedCandidate.STATUS_SEARCHED_SAVED_PIPELINE:
                type = "Searched Saved to Pipeline"
                subject = f"Candidate {type} by {str(request.state.user.first_name + ' ' + request.state.user.last_name)}"
                content = f"The following candidate has been saved in the pipeline to be later considered for the interview for <strong>{requisition['title']}</strong> role by the recruiter from <strong>{requisition['organization']['name']}</strong>"
                
            elif status == ShortlistedCandidate.STATUS_SEARCHED_SHORTLISTED:
                type = "Searched Shortlisted"
                subject = f"Candidate {type} by {str(request.state.user.first_name + ' ' + request.state.user.last_name)}"
                content = f"We’re excited to inform you that a candidate has been shortlisted for the <strong>{requisition['title']}</strong> role at <strong>{requisition['organization']['name']}</strong>. Please arrange the interview between the shortlisted candidate and the recruiter, from {requisition['organization']['name']}, at your earliest convenience."


            candidate = (
                db.query(User)
                .filter(User.id == shorlisted_candidate.candidate_id)
                .first()
            )
            email_body = {
                "name": f"{request.state.user.first_name} {request.state.user.last_name}",
                "job_title": requisition["title"],
                "company": requisition["organization"]["name"],
                "candidate": jsonable_encoder(candidate),
                "type": type.lower(),
                "recruiter": jsonable_encoder(request.state.user),
                "content": content,
            }
            # send email
            send_email_background(
                background_tasks,
                subject,
                CONTACT_US_EMAIL,
                email_body,
                "candidate_action_for_team.html",
            )
        except Exception as e:
            raise e

    def get_searched_shortlisted_candidates(self, request, db):
        try:
            page = int(request.query_params.get("page", 1))
            per_page = int(request.query_params.get("per_page", 10))
            status = request.query_params.get("status")

            query = (
                db.query(ShortlistedCandidate)
                .options(
                    joinedload(ShortlistedCandidate.user),
                    joinedload(ShortlistedCandidate.requisition),
                )
                .filter(
                        ShortlistedCandidate.recruiter_id == request.state.user.id,
                        ShortlistedCandidate.status == status)
            )
            total = query.count()
            shortlisted_candidates = (
                query.offset((page - 1) * per_page).limit(per_page).all()
            )
            for shortlisted_candidate in shortlisted_candidates:
                check_for_interview = (
                    db.query(Interview)
                    .filter(
                        Interview.requisition_id == shortlisted_candidate.requisition_id,
                        Interview.user_id == shortlisted_candidate.candidate_id,
                    )
                    .first()
                )
                
                if check_for_interview:
                    feedback = (
                        db.query(InterviewFeedback)
                        .filter(
                            InterviewFeedback.requisition_id == shortlisted_candidate.requisition_id,
                            InterviewFeedback.user_id == shortlisted_candidate.candidate_id,
                        )
                        .first()
                    )
                    shortlisted_candidate.feedback_id = feedback.id if feedback else None

                else:
                    shortlisted_candidate.feedback_id = None
                
            return {
                "pagination": {
                    "total": total,
                    "last_page": math.ceil(total / per_page),
                    "page": page,
                    "per_page": per_page,
                },
                "items": jsonable_encoder(shortlisted_candidates),
            }
        except Exception as e:
            raise e
