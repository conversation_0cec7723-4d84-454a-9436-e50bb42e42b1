from fastapi import HTTPException
from  models.state import State
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload
from fastapi.encoders import jsonable_encoder
from models.country import Country
from custom_exceptions import EntityNotFoundException

import logging

logger = logging.getLogger(__name__)


class StateServiceClass:

    def add_state(self, state,country_id, db):
        try:
            state_name = state
            status_code = None
            timezone = None
            existing_city = db.query(State).filter(State.name == state_name).first()
            if not existing_city:
                state = State(name = state_name, 
                              status = 0, 
                              country_id = country_id,
                              state_code = status_code,
                              time_zone = timezone

                              )
                db.add(state)
                db.commit()
                db.refresh(state)
                return state
        except SQLAlchemyError as e:
            raise HTTPException(status_code=400, detail=str(e))
        
    def get_states_by_country_id(self, country_id, db):
        try:
            if country_id:
                states = (
                    db.query(State)
                    .filter(State.country_id == country_id)
                    .options(joinedload(State.country))
                    .all()
                )
            else:
                states = db.query(State).options(joinedload(State.country)).all()
            return jsonable_encoder(states)
     
        except Exception as e:
            raise e
        
    def get_state_id(self,state_name, country_name, db):
        try:
            logger.info(f"Getting state id started")
            country_id = 0
            state_id = 0
            data = {}
            
            # Update country id if country exist
            if country_name is not None and country_name != "" and country_name.lower() != "null":
                country_object = db.query(Country).filter(
                    Country.name == country_name).first()
                if country_object:
                    country_id = country_object.id

                if country_id > 0 and state_name is not None and state_name != "" and state_name.lower() != "null":
                    state_object = db.query(State).filter(State.name == state_name).first()
                    if state_object:
                        state_id = state_object.id
            
                data = {"country_name": country_name, "country_id": country_id,"state_name": state_name, "state_id": state_id}
            logger.info(f"Getting state id completed")
            return data
        except Exception as e:
            logger.error(f"Error in getting state id: {str(e)}")
            raise e
        

    def get_state_by_id(self, state_id, db):
        try:
            logger.info(f"Getting state by id {state_id} started")
            state = db.query(State).filter(State.id == state_id).first()
            if not state:
                logger.error(f"State with id {state_id} not found")
                return ""
            logger.info(f"Getting state by id {state_id} completed")
            return state.name
        
        except Exception as e:
            logger.error(f"Error in getting state id: {str(e)}")
            raise e
        
    


