from fastapi import HTT<PERSON>Exception
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from database import get_db
from dependencies import SECRET_KEY, ALGORITHM
from models.recruiter import <PERSON><PERSON><PERSON>er
import jwt
from config.path import exclude_recruiter_prefixes, exclude_common_routes

class RecruiterAuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # add dynamic URLs here
        excluded_prefixes = exclude_recruiter_prefixes
        # add static URLs here
        excluded_paths = exclude_common_routes
        request_path = request.url.path
        if (
            not any(request_path.startswith(prefix) for prefix in excluded_prefixes)
            and request_path not in excluded_paths
        ):
            authorization: str = request.headers.get("Authorization")
            token = None

            if authorization and authorization.startswith("Bearer "):
                token = authorization.split(" ")[1]

            user = None
            if token:
                try:
                    payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
                    userEmail = payload.get("sub")
                    if userEmail:
                        db_gen = get_db()
                        db: Session = next(db_gen)
                        try:
                            user = db.query(Recruiter).filter(Recruiter.email == userEmail).first()
                        finally:
                            db_gen.close()
                except jwt.ExpiredSignatureError:
                    return JSONResponse(
                        status_code=401,
                        content={
                            "success": False,
                            "data": {},
                            "message": "Token has expired",
                            "status_code": 401,
                        },
                    )
                except jwt.InvalidTokenError:
                    return JSONResponse(
                        status_code=401,
                        content={
                            "success": False,
                            "data": {},
                            "message": "Invalid Token",
                            "status_code": 401,
                        },
                    )
            if not user:
                return JSONResponse(
                    status_code=401,
                    content={
                        "success": False,
                        "data": {},
                        "message": "Invalid User",
                        "status_code": 401,
                    },
                )
            request.state.user = user

        response = await call_next(request)
        return response
