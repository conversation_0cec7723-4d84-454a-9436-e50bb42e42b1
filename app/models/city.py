from sqlalchemy import Column, Integer, String, Text, BigInteger, ForeignKey
from database import Base
from sqlalchemy.orm import relationship

class City(Base):
    __tablename__ = "cities"
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(250))
    status = Column(Integer, default = 0)
    country_id = Column(Integer, ForeignKey('countries.id'))
    state_id=Column(Integer, ForeignKey('states.id'))

    country = relationship("Country")
    state = relationship("State")

