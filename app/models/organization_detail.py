from sqlalchemy import Column, Integer, String, Text, TIMESTAMP, func, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from database import Base


class OrganizationDetail(Base):
    __tablename__ = "organization_details"

    id = Column(Integer, primary_key=True, autoincrement=True)
    organization_id = Column(
        Integer, ForeignKey("organizations.id"), index=True, nullable=False
    )
    sub_title = Column(String(250), nullable=True)
    description = Column(Text, nullable=True)
    founded = Column(String(250), nullable=True)
    company_size = Column(String(100), nullable=True)
    revenue = Column(String(100), nullable=True)
    industry = Column(String(100), nullable=True)
    address = Column(Text, nullable=True)
    website = Column(String(100), nullable=True)
    specialities = Column(Text, nullable=True)
    linkedin_url = Column(String(250), nullable=True)  # ← New column added

    organization = relationship("Organization", back_populates="organization_detail")
