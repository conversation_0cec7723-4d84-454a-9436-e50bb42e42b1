from sqlalchemy import Column, Integer, String, Text, BigInteger, ForeignKey
from database import Base

class UserEvaluation(Base):
    __tablename__ = "user_evaluations"
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    requisition_id = Column(Integer, nullable=False)
    user_id = Column(Integer, nullable=False,index=True)
    feedback = Column(Text, nullable=False)
    status = Column(Integer, nullable=False)