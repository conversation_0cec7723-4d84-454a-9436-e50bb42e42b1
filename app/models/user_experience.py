from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Foreign<PERSON>ey, Integer, String, BigInteger, Date, Text
from sqlalchemy.orm import relationship, backref

from database import Base


class UserExperience(Base):
    __tablename__ = "user_experiences"
    id = Column(BigInteger, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    job_title = Column(String)
    emp_type = Column(String)
    to_date = Column(Date, nullable=True)
    from_date = Column(Date, nullable=True)
    description = Column(Text)
    city_id = Column(Integer,ForeignKey("cities.id"), nullable=True, index=True)
    location = Column(String, nullable=True)
    location_type = Column(String, nullable=True)

    user = relationship('User', back_populates='experiences')
    # city = relationship('City')
    organization = relationship('Organization')
    