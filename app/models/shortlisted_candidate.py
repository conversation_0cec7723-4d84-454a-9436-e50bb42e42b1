from sqlalchemy import Column, <PERSON><PERSON><PERSON><PERSON>, Integer, String, TIMESTAMP, func, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from database import Base
from sqlalchemy.orm import relationship

class ShortlistedCandidate(Base):
    __tablename__ = 'shortlisted_candidates'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    interview_id = Column(BigInteger, nullable=False)
    requisition_id = Column(BigInteger, ForeignKey("requisitions.id"), nullable=False)
    candidate_id = Column(BigInteger, ForeignKey("users.id"), nullable=False)
    recruiter_id = Column(BigInteger, nullable=False)
    feedback = Column(String, nullable=True)

    status = Column(Integer, nullable=False)

    user = relationship("User", back_populates="shortlisted_candidate")
    requisition = relationship("Requisition")

    STATUS_PENDING = 0
    STATUS_INTERVIEW_SCHEDULED = 1
    STATUS_SAVED_PIPELINE = 2
    STATUS_REJECTED = 3
    STATUS_SEARCHED_SAVED_PIPELINE = 4
    STATUS_SEARCHED_SHORTLISTED = 5

    def statusText(self):
        if self.status == self.STATUS_PENDING:
            return "Pending"
        elif self.status == self.STATUS_INTERVIEW_SCHEDULED:
            return "Interview Scheduled"
        elif self.status == self.STATUS_SAVED_PIPELINE:
            return "Saved to pipeline"
        elif self.status == self.STATUS_REJECTED:
            return "Rejected"
        elif self.status == self.STATUS_SEARCHED_SAVED_PIPELINE:
            return "Save to pipeline from search page"
        elif self.status == self.STATUS_SEARCHED_SHORTLISTED:
            return "Shortlisted from search page"
        else:
            return "Unknown Status"
