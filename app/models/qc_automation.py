from sqlalchemy import <PERSON>umn, Integer, ForeignKey, JSON, TIMESTAMP, func
from sqlalchemy.orm import relationship
from database import Base  # Adjust the import based on your project structure

class UserProfileRevisions(Base):
    __tablename__ = "user_profile_revisions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    data = Column(JSON, nullable=True)
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp())
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp()
    )

    # Optional: relationship to User model
    user = relationship("User", back_populates="qc_automation_entries")  # You need to define the reverse relation in User model
