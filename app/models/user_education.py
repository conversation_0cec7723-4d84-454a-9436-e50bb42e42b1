from sqlalchemy import <PERSON><PERSON>an, Column, Foreign<PERSON>ey, Integer, String, BigInteger, Date, Text
from sqlalchemy.orm import relationship, backref

from database import Base


class UserEducation(Base):
    __tablename__ = "user_educations"
    id = Column(BigInteger, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    school_id = Column(Integer, ForeignKey("schools.id"))
    degree = Column(String)
    field = Column(String, nullable=False)
    description = Column(Text)
    grade = Column(String, nullable=True)
    city_id = Column(Integer, ForeignKey("cities.id"), nullable=True, index=True)
    start_date = Column(Date, nullable=True)
    end_date = Column(Date, nullable=True)
    location = Column(String, nullable=True)

    # city = relationship("City")
    school = relationship("School")
    user = relationship("User", back_populates="educations")
