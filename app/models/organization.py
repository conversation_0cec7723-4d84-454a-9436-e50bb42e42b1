from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    BigInteger,
    Date,
    TIMESTAMP,
    func,
    ForeignKey,
)
from database import Base
from sqlalchemy.orm import relationship
from models.organization_detail import OrganizationDetail


class Organization(Base):
    __tablename__ = "organizations"
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(250))
    logo = Column(String(250))
    city_id = Column(Integer, ForeignKey("cities.id"))
    domain = Column(String(250), nullable=True)
    linkedin_id = Column(String(250), nullable=True)
    
    city = relationship("City")
    organization_detail = relationship(
        "OrganizationDetail", back_populates="organization", uselist=False
    )
