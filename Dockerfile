# Use the official Python image from the Docker Hub
FROM python:3.9-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && apt-get clean

# Set work directory
WORKDIR /vettio-backend/

# Install dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt
RUN pip install hashids

# Install Playwright and download browser binaries
#RUN pip install playwright
#RUN playwright install
#RUN playwright install-deps

# RUN apt install ffmpeg

# Copy project
COPY . .

EXPOSE 8000
